//+------------------------------------------------------------------+
//| RECOVERED_DIAMOND_EA.mq5 |
//| Reconstructed from Diamond Pattern Analysis |
//| Advanced Market Archaeology |
//| |
//| This Expert Advisor is a sophisticated market analysis and |
//| trading system, designed with a metaphor of geological diamond |
//| excavation. It identifies "diamond patterns" in market data, |
//| analyzes their characteristics, and aims to "recover" value. |
//| It includes advanced features like 'quantum' and 'seismic' |
//| analysis, and the unique ability to self-reconstruct its own |
//| trading logic based on discovered patterns. |
//+------------------------------------------------------------------+
#property copyright "Diamond Recovery Systems"
#property version "3.14"
#property strict

#include <Trade\\Trade.mqh> // For CTrade class and trading operations
#include <Indicators\\Indicators.mqh> // Standard Indicators like iRSI, iMACD, iBands, iVolume, iATR, iMomentum
#include <MQL5/Include/Files/File.mqh> // For advanced file operations (FileOpen, FileWrite, etc.)
#include <MQL5/Include/DateTime.mqh> // For MqlDateTime and time functions
#include <MQL5/Include/Math/Stat.mqh> // For statistical functions like MathStdDev

//--- Input parameters for the EA ---
input double LotSize = 0.01; // Initial Lot Size, will be adapted by generated EA
input double ClarityThreshold = 5.0; // Minimum clarity for pattern consideration
input int MagicNumber = 777777; // Unique identifier for orders
input string DominantPattern = "Undefined_Pattern"; // Most frequent pattern, adapted later
input bool UseQuantumSignals = true; // Enable/disable quantum signal processing
input double NoiseFilterThreshold = 0.0001; // Threshold for filtering noise in seismic data
input int MinDiamondsForEA = 5; // Minimum diamonds required to generate a new EA

//--- Global variables for indicator handles ---
int handle_rsi;
int handle_macd;
int handle_bbands;
int handle_volume;
int handle_atr;
int handle_momentum;

//--- Global trading object (for trade execution) ---
CTrade trade;

//--- Structure to store discovered diamond patterns ---
struct DiamondPattern
{
    datetime discovered_time;
    string pattern_type;
    double clarity_score;
    double carat_weight;
    string cut_quality;
    string color_grade;
    double formation_depth;
    double extraction_confidence;
    string geological_layer;
    double resonance_frequency;
    string crystalline_structure;
    double refraction_index;
};

//--- Global arrays and counters for discovered diamonds and analysis data ---
DiamondPattern discovered_diamonds[];
int diamond_count = 0;

struct SeismicReading
{
    datetime time;
    double price_change; // Magnitude of price change (points)
    double volume_spike; // Raw volume
};
SeismicReading seismic_data[];
int seismic_readings = 0;

double quantum_signatures[];
double pattern_heatmap[7][24]; // Day of week (0-6) x Hour (0-23)
double pattern_crystallization_rate = 0.0; // How frequently patterns are found

int geological_layer_count = 0;

//--- File paths for reports and reconstructed EA ---
string ExcavationReport = "Diamond_Excavation_Report.csv";
string ReconstructedEA = "RECOVERED_DIAMOND_EA.mq5";
string PatternMap = "Pattern_Geological_Map.txt"; // For advanced visualization/reporting

//--- Forward declarations for functions ---
double AnalyzeMicroPatterns();
string ClassifyRadarSignature(double reflection, int frequency);
double GetMarketQuantumState(int i, int j);
double CalculateExcavationProbability(int depth);
string ClassifyMarketEra(int layer);
double CalculateSedimentDensity(int layer);
void MonitorPatternCrystallization();
string ExtractDiscoveryMethod(string pattern_type);
double GetLastSeismicAmplitude();
double GetLastRadarReflection();
double GetLastQuantumSignature();
string GetCurrentMarketSession();
string GetCurrentStratigraphicEra();
string GetCurrentWeatherConditions();
double CalculateCurrentSedimentDensity();
double CalculateCurrentExcavationProbability();
bool DetectDiamondFormation(double amplitude, double frequency);
double CalculateBollingerPosition();
string ClassifyPatternType(double rsi_val, double macd_val, double bb_upper_val, double bb_lower_val);
double ArraySum(double &arr[]);
void CreateExcavationReportHeader();
void LogDiamondDiscovery(DiamondPattern& diamond);
void UpdatePatternHeatMap(DiamondPattern& diamond);
void GenerateReconstructedEA();
string GenerateEAHeader();
string GenerateInputParameters();
string GenerateMainTradingLogic();
string GenerateUtilityFunctions();
string GenerateInitFunction();
string GenerateDeinitFunction();
void GenerateFinalExcavationReport();
double CalculateAverageClarity();
double CalculateAverageCaratWeight();
string GetDominantPattern();
double GetHighestClarity();
double GetHeaviestCarat();
double CalculateTickIntensity();
double ScanForHiddenStructures(int frequency);
// --- HARMONIC DETECTION FORWARD DECLARATIONS ---
bool Harmonic_IsApproxEqual(double value, double target, double tolerance);
double Harmonic_CalculateRatio(double p1, double p2, double p3);
double Harmonic_CalculateExtension(double p1, double p2, double p3);
string Harmonic_GetPatternDirection(double x, double a);
bool Harmonic_CheckGartley(double x, double a, double b, double c, double d);
bool Harmonic_CheckBat(double x, double a, double b, double c, double d);
bool Harmonic_CheckButterfly(double x, double a, double b, double c, double d);
bool Harmonic_CheckCrab(double x, double a, double b, double c, double d);
bool Harmonic_CheckABCD(double a, double b, double c, double d);
string Harmonic_DetectPatterns(const double& points[]);
// --- END HARMONIC DETECTION FORWARD DECLARATIONS ---
double CalculateSoilHardness(int depth);
double CalculateWaterTableFactor(int depth);
double CalculateGeologicalStability(int depth);
double CalculateWeatherFactor();
double CalculateTimeOfDayFactor();
double CalculateEquipmentEfficiency();
double CalculatePatternClarity(double rsi, double macd_hist);


//+------------------------------------------------------------------+
//| Expert initialization function |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("💎 Reconstructed Diamond Recovery System: Initialization Commencing...");

    // Initialize the CTrade object
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetTypeFilling(ORDER_FILLING_FOK); // Fill or Kill order execution
    trade.SetDeviationInPoints(10); // 10 points deviation for order placement

    //--- Initialize standard indicator handles ---
    handle_rsi = iRSI(_Symbol, _Period, 14, PRICE_CLOSE);
    handle_macd = iMACD(_Symbol, _Period, 12, 26, 9, PRICE_CLOSE);
    handle_bbands = iBands(_Symbol, _Period, 20, 2, 0, PRICE_CLOSE);
    handle_volume = iVolume(_Symbol, _Period);
    handle_atr = iATR(_Symbol, _Period, 14);
    handle_momentum = iMomentum(_Symbol, _Period, 14, PRICE_CLOSE);

    // --- Critical Check: Validate Indicator Handles ---
    if(handle_rsi == INVALID_HANDLE || handle_macd == INVALID_HANDLE ||
       handle_bbands == INVALID_HANDLE || handle_volume == INVALID_HANDLE ||
       handle_atr == INVALID_HANDLE || handle_momentum == INVALID_HANDLE)
    {
        Print("CRITICAL ERROR: Failed to initialize one or more indicators. Last Error: ", GetLastError());
        return INIT_FAILED;
    }
    Print("All core indicators initialized successfully.");

    // Create the header for the excavation report CSV
    CreateExcavationReportHeader();

    // Initialize the pattern heatmap to zero
    ArrayInitialize(pattern_heatmap, 0.0);

    // Set up a timer for periodic tasks (e.g., monitoring crystallization, EA re-generation)
    EventSetTimer(60); // Trigger OnTimer every 60 seconds

    Print("💎 Reconstructed Diamond Recovery System: Initialization Complete. Ready for Refined Archaeology!");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== RECONSTRUCTED DIAMOND RECOVERY EXCAVATION COMPLETE ===");
    Print("Total Diamonds Recovered: ", diamond_count);

    // Generate the final summary report
    GenerateFinalExcavationReport();
    Print("Complete analysis saved to: ", ExcavationReport);
    Print("New Reconstructed EA: ", ReconstructedEA);

    //--- Release indicator handles to free resources ---
    IndicatorRelease(handle_rsi);
    IndicatorRelease(handle_macd);
    IndicatorRelease(handle_bbands);
    IndicatorRelease(handle_volume);
    IndicatorRelease(handle_atr);
    IndicatorRelease(handle_momentum);

    // Kill the timer to prevent further calls after deinitialization
    EventKillTimer();
}

//+------------------------------------------------------------------+
//| Expert tick function |
//| Processes incoming price ticks for real-time analysis and trading|
//+------------------------------------------------------------------+
void OnTick()
{
    static double last_known_bid = 0.0;
    static double last_known_ask = 0.0;
    static datetime last_seismic_time = 0; // To prevent processing multiple ticks at the same time

    MqlTick current_tick;
    if(!SymbolInfoTick(_Symbol, current_tick))
    {
        // If tick data cannot be obtained, exit
        return;
    }

    // Prevent redundant processing for the same tick (same time and price)
    if (current_tick.time == last_seismic_time && current_tick.bid == last_known_bid && current_tick.ask == last_known_ask)
    {
        return;
    }

    double current_price_mid = (current_tick.bid + current_tick.ask) / 2.0;

    // Get previous bar's close price for price change amplitude calculation
    double previous_bar_close[1];
    if (CopyClose(_Symbol, _Period, 1, 1, previous_bar_close) <= 0)
    {
        previous_bar_close[0] = current_price_mid; // Fallback to current price if no previous bar
    }

    // Calculate price change amplitude and volume spike
    double price_change_amplitude = MathAbs(current_price_mid - previous_bar_close[0]) / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double volume_spike_indicator = current_tick.last_volume;

    // Store seismic reading for later analysis
    int current_seismic_idx = ArraySize(seismic_data);
    ArrayResize(seismic_data, current_seismic_idx + 1);
    seismic_data[current_seismic_idx].time = current_tick.time;
    seismic_data[current_seismic_idx].price_change = price_change_amplitude;
    seismic_data[current_seismic_idx].volume_spike = volume_spike_indicator;
    seismic_readings++;

    // --- Core Adaptation Logic (Dynamically generated trading rules) ---
    // The following variables represent insights derived from prior "diamond" discoveries.
    string dominant_pattern_found = GetDominantPattern();
    double avg_clarity = CalculateAverageClarity();
    double avg_carat = CalculateAverageCaratWeight();

    // Print current adaptive insights (for debugging/monitoring)
    Print("\n // --- Adaptive Trading Logic based on Dominant Pattern ---");
    Print(" // Dominant Pattern identified: \"", dominant_pattern_found, "\"");
    Print(" // Average Diamond Clarity: ", DoubleToString(avg_clarity, 2));
    Print(" // Average Diamond Carat: ", DoubleToString(avg_carat, 4), "\n");

    // Attempt to detect a "diamond formation" (significant pattern)
    if (DetectDiamondFormation(price_change_amplitude, volume_spike_indicator))
    {
        DiamondPattern new_diamond; // Create a new DiamondPattern structure

        // Retrieve indicator values for classification and scoring
        double rsi_val[1], macd_main[1], macd_signal[1], bb_upper[1], bb_lower[1], bb_mid[1];
        double atr_val[1];

        // Ensure all required indicator buffers can be copied
        if (CopyBuffer(handle_rsi, 0, 0, 1, rsi_val) > 0 &&
            CopyBuffer(handle_macd, 0, 0, 1, macd_main) > 0 &&
            CopyBuffer(handle_macd, 1, 0, 1, macd_signal) > 0 &&
            CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, bb_upper) > 0 &&
            CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, bb_lower) > 0 &&
            CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, bb_mid) > 0 &&
            CopyBuffer(handle_atr, 0, 0, 1, atr_val) > 0)
        {
            // Populate diamond attributes based on current market state
            new_diamond.discovered_time = TimeCurrent();
            new_diamond.clarity_score = CalculatePatternClarity(rsi_val[0], macd_main[0] - macd_signal[0]);
            new_diamond.pattern_type = ClassifyPatternType(rsi_val[0], macd_main[0] - macd_signal[0], bb_upper[0], bb_lower[0]);
            new_diamond.carat_weight = atr_val[0] * 1000; // Carat weight proportional to ATR (volatility)
            new_diamond.cut_quality = (price_change_amplitude > SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10) ? "Excellent" : "Good";
            new_diamond.color_grade = GetCurrentWeatherConditions();
            new_diamond.formation_depth = Bars(_Symbol, _Period) - 1; // Depth from current bar
            new_diamond.extraction_confidence = CalculateCurrentExcavationProbability() * 100.0;
            new_diamond.geological_layer = GetCurrentStratigraphicEra();
            new_diamond.resonance_frequency = AnalyzeMicroPatterns();
            new_diamond.crystalline_structure = (MathAbs(CalculateBollingerPosition()) < 0.5) ? "Range-Bound" : "Trend-Aligned";
            new_diamond.refraction_index = CalculateBollingerPosition();
        }
        else
        {
            // Default values if indicator data is missing for robustness
            new_diamond.discovered_time = TimeCurrent();
            new_diamond.clarity_score = 1.0;
            new_diamond.carat_weight = 0.05;
            new_diamond.pattern_type = "Unclassified_Seismic_Diamond";
            new_diamond.cut_quality = "Fair";
            new_diamond.color_grade = "Z";
            new_diamond.formation_depth = 0;
            new_diamond.extraction_confidence = 0;
            new_diamond.geological_layer = "Unknown";
            new_diamond.resonance_frequency = 0;
            new_diamond.crystalline_structure = "Undefined";
            new_diamond.refraction_index = 0;
        }

        // Add the new diamond to the global array
        int current_diamond_idx = ArraySize(discovered_diamonds);
        ArrayResize(discovered_diamonds, current_diamond_idx + 1);
        discovered_diamonds[current_diamond_idx] = new_diamond;
        diamond_count++;

        // Log and print discovery details
        Print("💎 Diamond Detected! Type: ", new_diamond.pattern_type, ", Clarity: ", DoubleToString(new_diamond.clarity_score, 2), ", Carat: ", DoubleToString(new_diamond.carat_weight, 4));
        LogDiamondDiscovery(new_diamond);
        UpdatePatternHeatMap(new_diamond);

        // --- Dynamic Trade Execution Logic (Adaptive) ---
        double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        // Trade based on the identified dominant pattern and clarity/confidence
        if (dominant_pattern_found == "Bullish_Trend_Continuation" && avg_clarity > ClarityThreshold)
        {
            if (StringFind(new_diamond.pattern_type, "Bullish") >= 0 && new_diamond.extraction_confidence > 70.0)
            {
                if (!trade.Buy(LotSize, _Symbol, current_ask, 0, 0, "Bullish Diamond Entry"))
                {
                    Print("Failed to place BUY order: ", trade.ResultRetcode(), " - ", trade.ResultDealComment());
                }
                else
                {
                    Print("BUY Order placed based on Bullish Trend Diamond!");
                }
            }
        }
        else if (dominant_pattern_found == "Harmonic_Reversal_Pattern" && avg_clarity > ClarityThreshold)
        {
            if (StringFind(new_diamond.pattern_type, "Harmonic_Reversal") >= 0 && new_diamond.extraction_confidence > 75.0)
            {
                // For a harmonic reversal, assuming it implies a reversal against the current trend
                // (This would need more refined logic about the specific harmonic pattern's direction)
                if (!trade.Sell(LotSize, _Symbol, current_bid, 0, 0, "Bearish Harmonic Diamond"))
                {
                    Print("Failed to place SELL order: ", trade.ResultRetcode(), " - ", trade.ResultDealComment());
                }
                else
                {
                    Print("SELL Order placed based on Harmonic Reversal Diamond!");
                }
            }
        }
        else // Default/Fallback logic if no specific dominant strategy or conditions not met
        {
            if (new_diamond.clarity_score > ClarityThreshold && new_diamond.extraction_confidence > 60.0)
            {
                // General logic: if pattern is clear enough, try to trade based on its classification
                if (StringFind(new_diamond.pattern_type, "Bullish") >= 0 || StringFind(new_diamond.pattern_type, "Oversold") >= 0)
                {
                    if (!trade.Buy(LotSize, _Symbol, current_ask, 0, 0, "Generic Buy Diamond"))
                    {
                        Print("Failed to place Generic Buy order: ", trade.ResultRetcode(), " - ", trade.ResultDealComment());
                    }
                    else
                    {
                        Print("Generic BUY from Diamond!");
                    }
                }
                else if (StringFind(new_diamond.pattern_type, "Bearish") >= 0 || StringFind(new_diamond.pattern_type, "Overbought") >= 0)
                {
                    if (!trade.Sell(LotSize, _Symbol, current_bid, 0, 0, "Generic Sell Diamond"))
                    {
                        Print("Failed to place Generic Sell order: ", trade.ResultRetcode(), " - ", trade.ResultDealComment());
                    }
                    else
                    {
                        Print("Generic SELL from Diamond!");
                    }
                }
            }
        }
    } // End of if (DetectDiamondFormation)

    // Update static tick info for next tick's redundancy check
    last_known_bid = current_tick.bid;
    last_known_ask = current_tick.ask;
    last_seismic_time = current_tick.time;
}

//+------------------------------------------------------------------+
//| Expert timer function |
//| Triggered periodically (e.g., every minute) for less frequent |
//| but important tasks like monitoring crystallization and EA re-gen.|
//+------------------------------------------------------------------+
void OnTimer()
{
    // Monitor Pattern Crystallization Rate
    MonitorPatternCrystallization();

    // Check if enough "diamonds" have been found for EA reconstruction
    if (diamond_count >= MinDiamondsForEA)
    {
        Print("💎 Critical Mass Achieved! Initiating Self-Reconstruction Sequence...");
        GenerateReconstructedEA();
        // You might want to deinit the current EA and suggest reloading the new one.
        // Or simply let it continue and allow manual reload.
        Print("New EA '", ReconstructedEA, "' generated. Please compile and attach to apply changes.");
        // Reset diamond_count if you want to generate EAs periodically
        // diamond_count = 0; // Or reset after a successful trade based on generated EA
    }
}
//+------------------------------------------------------------------+
//| Self-Reconstruction and Utility Functions |
//| This section contains the core logic for the EA to analyze its |
//| own performance and discovered patterns, then write a new, |
//| optimized version of itself. |
//+------------------------------------------------------------------+

// Function: GenerateReconstructedEA
// Description: Orchestrates the generation of the new RECOVERED_DIAMOND_EA.mq5 file.
// This is the heart of the self-reconstruction capability.
void GenerateReconstructedEA()
{
    // Build the complete source code string for the new EA.
    string ea_code = "";
    ea_code += GenerateEAHeader();
    ea_code += GenerateInputParameters();
    ea_code += GenerateInitFunction();
    ea_code += GenerateDeinitFunction();
    ea_code += GenerateOnTickLogic(); // This is the main trading logic
    ea_code += GenerateOnTimerLogic(); // And the periodic tasks
    ea_code += GenerateUtilityFunctions(); // All helper and analysis functions

    // Attempt to write the generated code to a new .mq5 file.
    int file_handle = FileOpen(ReconstructedEA, FILE_WRITE | FILE_TXT | FILE_ANSI);
    if (file_handle != INVALID_HANDLE)
    {
        FileWriteString(file_handle, ea_code);
        FileClose(file_handle);
        Print("SUCCESS: New EA '", ReconstructedEA, "' generated successfully.");
        Print("Please recompile and attach the new EA to apply the adaptive changes.");
    }
    else
    {
        Print("ERROR: Failed to write reconstructed EA file. Error: ", GetLastError());
    }
}

// Function: GenerateEAHeader
// Description: Generates the standard MQL5 header for the new EA.
string GenerateEAHeader()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| RECOVERED_DIAMOND_EA.mq5 |\n";
    code += "//| Reconstructed from Diamond Pattern Analysis |\n";
    code += "//| Adaptive Market Archaeology |\n";
    code += "//| |\n";
    code += "//| This Expert Advisor has been self-reconstructed on " + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + ".\n";
    code += "//| It incorporates insights from " + IntegerToString(diamond_count) + " discovered diamond patterns.\n";
    code += "//| |\n";
    code += "//+------------------------------------------------------------------+\n";
    code += "#property copyright \"" + _LastError + "\"\n"; // Dynamically add last error info for debugging
    code += "#property version \"" + DoubleToString(MQL_VERSION / 100.0, 2) + "\"\n"; // Use MQL version
    code += "#property strict\n\n";
    code += "#include <Trade\\Trade.mqh>\n";
    code += "#include <Indicators\\Indicators.mqh>\n";
    code += "#include <MQL5/Include/Files/File.mqh>\n";
    code += "#include <MQL5/Include/DateTime.mqh>\n";
    code += "#include <MQL5/Include/Math/Stat.mqh>\n\n";
    return code;
}

// Function: GenerateInputParameters
// Description: Generates the input parameters for the new EA, adapting them based on findings.
string GenerateInputParameters()
{
    string code = "//--- Input parameters for the EA ---\n";

    // Adaptive Lot Size: Could be based on average carat weight or overall clarity
    double adapted_lotsize = LotSize;
    if (diamond_count > 0)
    {
        adapted_lotsize = MathMax(0.01, MathRound(CalculateAverageCaratWeight() / 1000.0 * 100.0) / 100.0); // Simple adaptation
        if (adapted_lotsize > 1.0) adapted_lotsize = 1.0; // Cap lot size
    }
    code += "input double LotSize = " + DoubleToString(adapted_lotsize, 2) + "; // Adapted Lot Size\n";

    // Adaptive Clarity Threshold: Higher if patterns are consistently clear, lower if struggling to find them
    double adapted_clarity_threshold = ClarityThreshold;
    if (diamond_count > 0)
    {
        adapted_clarity_threshold = MathMax(2.0, MathMin(8.0, CalculateAverageClarity() * 0.9)); // Adjust based on avg clarity
    }
    code += "input double ClarityThreshold = " + DoubleToString(adapted_clarity_threshold, 2) + "; // Adapted Minimum clarity for pattern consideration\n";

    code += "input int MagicNumber = " + IntegerToString(MagicNumber) + "; // Unique identifier for orders\n";

    // Dominant Pattern: Explicitly set the detected dominant pattern for the new EA
    string new_dominant_pattern = GetDominantPattern();
    if (new_dominant_pattern == "No_Dominant_Pattern_Yet") new_dominant_pattern = "Undefined_Pattern";
    code += "input string DominantPattern = \"" + new_dominant_pattern + "\"; // Most frequent pattern, dynamically set\n";

    code += "input bool UseQuantumSignals = " + (UseQuantumSignals ? "true" : "false") + "; // Enable/disable quantum signal processing\n";
    code += "input double NoiseFilterThreshold = " + DoubleToString(NoiseFilterThreshold, 5) + "; // Threshold for filtering noise in seismic data\n";
    code += "input int MinDiamondsForEA = " + IntegerToString(MinDiamondsForEA) + "; // Minimum diamonds required to generate a new EA\n\n";

    code += "//--- Global variables for indicator handles ---\n";
    code += "int handle_rsi;\n";
    code += "int handle_macd;\n";
    code += "int handle_bbands;\n";
    code += "int handle_volume;\n";
    code += "int handle_atr;\n";
    code += "int handle_momentum;\n\n";

    code += "//--- Global trading object (for trade execution) ---\n";
    code += "CTrade trade;\n\n";

    code += "//--- Structure to store discovered diamond patterns ---\n";
    code += "struct DiamondPattern\n";
    code += "{\n";
    code += " datetime discovered_time;\n";
    code += " string pattern_type;\n";
    code += " double clarity_score;\n";
    code += " double carat_weight;\n";
    code += " string cut_quality;\n";
    code += " string color_grade;\n";
    code += " double formation_depth;\n";
    code += " double extraction_confidence;\n";
    code += " string geological_layer;\n";
    code += " double resonance_frequency;\n";
    code += " string crystalline_structure;\n";
    code += " double refraction_index;\n";
    code += "};\n\n";

    code += "//--- Global arrays and counters for discovered diamonds and analysis data ---\n";
    code += "DiamondPattern discovered_diamonds[];\n";
    code += "int diamond_count = 0;\n\n";

    code += "struct SeismicReading\n";
    code += "{\n";
    code += " datetime time;\n";
    code += " double price_change; // Magnitude of price change (points)\n";
    code += " double volume_spike; // Raw volume\n";
    code += "};\n";
    code += "SeismicReading seismic_data[];\n";
    code += "int seismic_readings = 0;\n\n";

    code += "double quantum_signatures[];\n";
    code += "double pattern_heatmap[7][24]; // Day of week (0-6) x Hour (0-23)\n";
    code += "double pattern_crystallization_rate = 0.0;\n\n";
    code += "int geological_layer_count = 0;\n\n";

    code += "//--- File paths for reports and reconstructed EA ---\n";
    code += "string ExcavationReport = \"" + ExcavationReport + "\";\n";
    code += "string ReconstructedEA = \"" + ReconstructedEA + "\";\n";
    code += "string PatternMap = \"" + PatternMap + "\";\n\n";
    return code;
}

// Function: GenerateInitFunction
// Description: Generates the OnInit() function for the new EA.
string GenerateInitFunction()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| Expert initialization function |\n";
    code += "//+------------------------------------------------------------------+\n";
    code += "int OnInit()\n";
    code += "{\n";
    code += " Print(\"💎 Reconstructed Diamond Recovery System: Initialization Commencing...\");\n\n";
    code += " trade.SetExpertMagicNumber(MagicNumber);\n";
    code += " trade.SetTypeFilling(ORDER_FILLING_FOK);\n";
    code += " trade.SetDeviationInPoints(10);\n\n";
    code += " handle_rsi = iRSI(_Symbol, _Period, 14, PRICE_CLOSE);\n";
    code += " handle_macd = iMACD(_Symbol, _Period, 12, 26, 9, PRICE_CLOSE);\n";
    code += " handle_bbands = iBands(_Symbol, _Period, 20, 2, 0, PRICE_CLOSE);\n";
    code += " handle_volume = iVolume(_Symbol, _Period);\n";
    code += " handle_atr = iATR(_Symbol, _Period, 14);\n";
    code += " handle_momentum = iMomentum(_Symbol, _Period, 14, PRICE_CLOSE);\n\n";
    code += " if(handle_rsi == INVALID_HANDLE || handle_macd == INVALID_HANDLE ||\n";
    code += " handle_bbands == INVALID_HANDLE || handle_volume == INVALID_HANDLE ||\n";
    code += " handle_atr == INVALID_HANDLE || handle_momentum == INVALID_HANDLE)\n";
    code += " {\n";
    code += " Print(\"CRITICAL ERROR: Failed to initialize one or more indicators. Last Error: \", GetLastError());\n";
    code += " return INIT_FAILED;\n";
    code += " }\n";
    code += " Print(\"All core indicators initialized successfully.\");\n\n";
    code += " CreateExcavationReportHeader();\n";
    code += " ArrayInitialize(pattern_heatmap, 0.0);\n";
    code += " EventSetTimer(60); // Timer for periodic tasks\n\n";
    code += " Print(\"💎 Reconstructed Diamond Recovery System: Initialization Complete. Ready for Refined Archaeology!\");\n";
    code += " return(INIT_SUCCEEDED);\n";
    code += "}\n\n";
    return code;
}

// Function: GenerateDeinitFunction
// Description: Generates the OnDeinit() function for the new EA.
string GenerateDeinitFunction()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| Expert deinitialization function |\n";
    code += "//+------------------------------------------------------------------+\n";
    code += "void OnDeinit(const int reason)\n";
    code += "{\n";
    code += " Print(\"=== RECONSTRUCTED DIAMOND RECOVERY EXCAVATION COMPLETE ===\");\n";
    code += " Print(\"Total Diamonds Recovered: \", diamond_count);\n\n";
    code += " GenerateFinalExcavationReport();\n";
    code += " Print(\"Complete analysis saved to: \", ExcavationReport);\n";
    code += " Print(\"New Reconstructed EA: \", ReconstructedEA);\n\n";
    code += " IndicatorRelease(handle_rsi);\n";
    code += " IndicatorRelease(handle_macd);\n";
    code += " IndicatorRelease(handle_bbands);\n";
    code += " IndicatorRelease(handle_volume);\n";
    code += " IndicatorRelease(handle_atr);\n";
    code += " IndicatorRelease(handle_momentum);\n\n";
    code += " EventKillTimer();\n";
    code += "}\n\n";
    return code;
}

// Function: GenerateOnTickLogic
// Description: Generates the OnTick() function with dynamic trading rules.
string GenerateOnTickLogic()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| Expert tick function |\n";
    code += "//+------------------------------------------------------------------+\n";
    code += "void OnTick()\n";
    code += "{\n";
    code += " static double last_known_bid = 0.0;\n";
    code += " static double last_known_ask = 0.0;\n";
    code += " static datetime last_seismic_time = 0;\n\n";
    code += " MqlTick current_tick;\n";
    code += " if(!SymbolInfoTick(_Symbol, current_tick))\n";
    code += " {\n";
    code += " return;\n";
    code += " }\n\n";
    code += " if (current_tick.time == last_seismic_time && current_tick.bid == last_known_bid && current_tick.ask == last_known_ask)\n";
    code += " {\n";
    code += " return;\n";
    code += " }\n\n";
    code += " double current_price_mid = (current_tick.bid + current_tick.ask) / 2.0;\n";
    code += " double previous_bar_close[1];\n";
    code += " if (CopyClose(_Symbol, _Period, 1, 1, previous_bar_close) <= 0)\n";
    code += " {\n";
    code += " previous_bar_close[0] = current_price_mid;\n";
    code += " }\n\n";
    code += " double price_change_amplitude = MathAbs(current_price_mid - previous_bar_close[0]) / SymbolInfoDouble(_Symbol, SYMBOL_POINT);\n";
    code += " double volume_spike_indicator = current_tick.last_volume;\n\n";
    code += " int current_seismic_idx = ArraySize(seismic_data);\n";
    code += " ArrayResize(seismic_data, current_seismic_idx + 1);\n";
    code += " seismic_data[current_seismic_idx].time = current_tick.time;\n";
    code += " seismic_data[current_seismic_idx].price_change = price_change_amplitude;\n";
    code += " seismic_data[current_seismic_idx].volume_spike = volume_spike_indicator;\n";
    code += " seismic_readings++;\n\n";

    code += " string dominant_pattern_found = DominantPattern; // Use the adapted input parameter\n";
    code += " double avg_clarity = CalculateAverageClarity();\n";
    code += " double avg_carat = CalculateAverageCaratWeight();\n\n";

    code += " if (DetectDiamondFormation(price_change_amplitude, volume_spike_indicator))\n";
    code += " {\n";
    code += " DiamondPattern new_diamond;\n";
    code += " double rsi_val[1], macd_main[1], macd_signal[1], bb_upper[1], bb_lower[1], bb_mid[1];\n";
    code += " double atr_val[1], momentum_val[1], volume_val[1];\n\n";
    code += " if (CopyBuffer(handle_rsi, 0, 0, 1, rsi_val) > 0 &&\n";
    code += " CopyBuffer(handle_macd, 0, 0, 1, macd_main) > 0 &&\n";
    code += " CopyBuffer(handle_macd, 1, 0, 1, macd_signal) > 0 &&\n";
    code += " CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, bb_upper) > 0 &&\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, bb_lower) > 0 &&\n";
    code += " CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, bb_mid) > 0 &&\n";
    code += " CopyBuffer(handle_atr, 0, 0, 1, atr_val) > 0 &&\n";
    code += " CopyBuffer(handle_momentum, 0, 0, 1, momentum_val) > 0 &&\n";
    code += " CopyBuffer(handle_volume, 0, 0, 1, volume_val) > 0)\n";
    code += " {\n";
    code += " new_diamond.discovered_time = TimeCurrent();\n";
    code += " new_diamond.clarity_score = CalculatePatternClarity(rsi_val[0], macd_main[0] - macd_signal[0]);\n";
    code += " new_diamond.pattern_type = ClassifyPatternType(rsi_val[0], macd_main[0] - macd_signal[0], bb_upper[0], bb_lower[0]);\n";

    // --- Dynamic Harmonic Pattern Integration ---
    code += " double harmonic_points[5];\n";
    code += " double last_closes[5];\n";
    code += " if (CopyClose(_Symbol, _Period, 0, 5, last_closes) == 5)\n";
    code += " {\n";
    code += " harmonic_points[0] = last_closes[4]; // X\n";
    code += " harmonic_points[1] = last_closes[3]; // A\n";
    code += " harmonic_points[2] = last_closes[2]; // B\n";
    code += " harmonic_points[3] = last_closes[1]; // C\n";
    code += " harmonic_points[4] = last_closes[0]; // D\n";
    code += " string detected_harmonic = Harmonic_DetectPatterns(harmonic_points);\n";
    code += " if (detected_harmonic != \"No_Harmonic_Detected_Yet\" &&\n";
    code += " detected_harmonic != \"Not_Enough_Points_For_Harmonic\")\n";
    code += " {\n";
    code += " new_diamond.pattern_type = detected_harmonic;\n";
    code += " new_diamond.clarity_score = MathMin(10.0, new_diamond.clarity_score + 3.0); // Boost clarity\n";
    code += " }\n";
    code += " }\n";
    // --- End Dynamic Harmonic Pattern Integration ---

    code += " new_diamond.carat_weight = atr_val[0] * 1000;\n";
    code += " new_diamond.cut_quality = (price_change_amplitude > SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10) ? \"Excellent\" : \"Good\";\n";
    code += " new_diamond.color_grade = GetCurrentWeatherConditions();\n";
    code += " new_diamond.formation_depth = Bars(_Symbol, _Period) - 1;\n";
    code += " new_diamond.extraction_confidence = CalculateCurrentExcavationProbability() * 100.0;\n";
    code += " new_diamond.geological_layer = GetCurrentStratigraphicEra();\n";
    code += " new_diamond.resonance_frequency = AnalyzeMicroPatterns();\n";
    code += " new_diamond.crystalline_structure = (MathAbs(CalculateBollingerPosition()) < 0.5) ? \"Range-Bound\" : \"Trend-Aligned\";\n";
    code += " new_diamond.refraction_index = CalculateBollingerPosition();\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " new_diamond.discovered_time = TimeCurrent();\n";
    code += " new_diamond.clarity_score = 1.0;\n";
    code += " new_diamond.carat_weight = 0.05;\n";
    code += " new_diamond.pattern_type = \"Unclassified_Seismic_Diamond\";\n";
    code += " new_diamond.cut_quality = \"Fair\";\n";
    code += " new_diamond.color_grade = \"Z\";\n";
    code += " new_diamond.formation_depth = 0;\n";
    code += " new_diamond.extraction_confidence = 0;\n";
    code += " new_diamond.geological_layer = \"Unknown\";\n";
    code += " new_diamond.resonance_frequency = 0;\n";
    code += " new_diamond.crystalline_structure = \"Undefined\";\n";
    code += " new_diamond.refraction_index = 0;\n";
    code += " }\n\n";
    code += " int current_diamond_idx = ArraySize(discovered_diamonds);\n";
    code += " ArrayResize(discovered_diamonds, current_diamond_idx + 1);\n";
    code += " discovered_diamonds[current_diamond_idx] = new_diamond;\n";
    code += " diamond_count++;\n\n";
    code += " Print(\"💎 Diamond Detected! Type: \", new_diamond.pattern_type, \", Clarity: \", DoubleToString(new_diamond.clarity_score, 2), \", Carat: \", DoubleToString(new_diamond.carat_weight, 4));\n";
    code += " LogDiamondDiscovery(new_diamond);\n";
    code += " UpdatePatternHeatMap(new_diamond);\n\n";
    code += " double current_ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);\n";
    code += " double current_bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);\n\n";

    // --- Adaptive Trading Logic based on Dominant Pattern ---
    code += " if (dominant_pattern_found == \"Bullish_Trend_Continuation\" && avg_clarity > ClarityThreshold)\n";
    code += " {\n";
    code += " if (StringFind(new_diamond.pattern_type, \"Bullish\") >= 0 && new_diamond.extraction_confidence > 70.0)\n";
    code += " {\n";
    code += " if (!trade.Buy(LotSize, _Symbol, current_ask, 0, 0, \"Bullish Diamond Entry\"))\n";
    code += " {\n";
    code += " Print(\"Failed to place BUY order: \", trade.ResultRetcode(), \" - \", trade.ResultDealComment());\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"BUY Order placed based on Bullish Trend Diamond!\");\n";
    code += " }\n";
    code += " }\n";
    code += " }\n";
    code += " else if (dominant_pattern_found == \"Harmonic_Reversal_Pattern\" && avg_clarity > ClarityThreshold)\n";
    code += " {\n";
    code += " if (StringFind(new_diamond.pattern_type, \"Harmonic_Reversal\") >= 0 && new_diamond.extraction_confidence > 75.0)\n";
    code += " {\n";
    code += " if (!trade.Sell(LotSize, _Symbol, current_bid, 0, 0, \"Bearish Harmonic Diamond\"))\n";
    code += " {\n";
    code += " Print(\"Failed to place SELL order: \", trade.ResultRetcode(), \" - \", trade.ResultDealComment());\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"SELL Order placed based on Harmonic Reversal Diamond!\");\n";
    code += " }\n";
    code += " }\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " if (new_diamond.clarity_score > ClarityThreshold && new_diamond.extraction_confidence > 60.0)\n";
    code += " {\n";
    code += " if (StringFind(new_diamond.pattern_type, \"Bullish\") >= 0 || StringFind(new_diamond.pattern_type, \"Oversold\") >= 0)\n";
    code += " {\n";
    code += " if (!trade.Buy(LotSize, _Symbol, current_ask, 0, 0, \"Generic Buy Diamond\"))\n";
    code += " {\n";
    code += " Print(\"Failed to place Generic Buy order: \", trade.ResultRetcode(), \" - \", trade.ResultDealComment());\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"Generic BUY from Diamond!\");\n";
    code += " }\n";
    code += " }\n";
    code += " else if (StringFind(new_diamond.pattern_type, \"Bearish\") >= 0 || StringFind(new_diamond.pattern_type, \"Overbought\") >= 0)\n";
    code += " {\n";
    code += " if (!trade.Sell(LotSize, _Symbol, current_bid, 0, 0, \"Generic Sell Diamond\"))\n";
    code += " {\n";
    code += " Print(\"Failed to place Generic Sell order: \", trade.ResultRetcode(), \" - \", trade.ResultDealComment());\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"Generic SELL from Diamond!\");\n";
    code += " }\n";
    code += " }\n";
    code += " }\n";
    code += " }\n";
    code += " }\n\n";
    code += " last_known_bid = current_tick.bid;\n";
    code += " last_known_ask = current_tick.ask;\n";
    code += " last_seismic_time = current_tick.time;\n";
    code += "}\n\n";
    return code;
}

// Function: GenerateOnTimerLogic
// Description: Generates the OnTimer() function for the new EA.
string GenerateOnTimerLogic()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| Expert timer function |\n";
    code += "//+------------------------------------------------------------------+\n";
    code += "void OnTimer()\n";
    code += "{\n";
    code += " MonitorPatternCrystallization();\n\n";
    code += " if (diamond_count >= MinDiamondsForEA)\n";
    code += " {\n";
    code += " Print(\"💎 Critical Mass Achieved! Initiating Self-Reconstruction Sequence...\");\n";
    code += " GenerateReconstructedEA();\n";
    code += " Print(\"New EA '\", ReconstructedEA, \"' generated. Please compile and attach to apply changes.\");\n";
    code += " diamond_count = 0; // Reset count for next generation\n";
    code += " }\n";
    code += "}\n\n";
    return code;
}

// Function: GenerateUtilityFunctions
// Description: Generates the full implementations of all helper and analysis functions.
// This is critical for the self-reconstruction, as it includes the complex harmonic detection.
string GenerateUtilityFunctions()
{
    string code = "//+------------------------------------------------------------------+\n";
    code += "//| Utility and Analysis Functions (Generated) |\n";
    code += "//+------------------------------------------------------------------+\n\n";

    // Implementations for all functions previously declared and partially implemented in Section 1
    code += "double AnalyzeMicroPatterns()\n";
    code += "{\n";
    code += " double atr_val[1], momentum_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return 0.0;\n";
    code += " if (CopyBuffer(handle_momentum, 0, 0, 1, momentum_val) <= 0) return 0.0;\n";
    code += " return (atr_val[0] * 1000.0) + MathAbs(momentum_val[0]);\n";
    code += "}\n\n";

    code += "string ClassifyRadarSignature(double reflection, int frequency)\n";
    code += "{\n";
    code += " if (reflection > 0.8 && frequency > 50) return \"Strong_Echo_High_Frequency\";\n";
    code += " if (reflection > 0.5 && frequency > 20) return \"Moderate_Echo_Medium_Frequency\";\n";
    code += " return \"Weak_Echo_Low_Frequency\";\n";
    code += "}\n\n";

    code += "double GetMarketQuantumState(int i, int j)\n";
    code += "{\n";
    code += " MqlRates rates[2];\n";
    code += " if (CopyRates(_Symbol, _Period, i, 2, rates) != 2) return 0.0;\n";
    code += " return (rates[0].close + rates[0].open) / 2.0 * rates[1].tick_volume;\n";
    code += "}\n\n";

    code += "double CalculateExcavationProbability(int depth)\n";
    code += "{\n";
    code += " if (depth < 0) return 0.0;\n";
    code += " double atr_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return 0.0;\n";
    code += " double volatility_factor = MathMin(1.0, atr_val[0] / (SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 50));\n";
    code += " double depth_factor = MathMax(0.1, 1.0 - (double)depth / Bars(_Symbol, _Period) / 2.0);\n";
    code += " return 0.5 + (volatility_factor * 0.2) + (depth_factor * 0.3);\n";
    code += "}\n\n";

    code += "string ClassifyMarketEra(int layer)\n";
    code += "{\n";
    code += " double b_upper[1], b_lower[1], b_mid[1];\n";
    code += " double atr_val[1];\n";
    code += " if (CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, b_upper) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, b_lower) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, b_mid) <= 0 ||\n";
    code += " CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return \"Unknown_Era\";\n";
    code += " double band_width = b_upper[0] - b_lower[0];\n";
    code += " double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);\n";
    code += " double trending_threshold = atr_val[0] * 2.0;\n";
    code += " double ranging_threshold = atr_val[0] * 0.5;\n";
    code += " if (band_width > trending_threshold && (current_price > b_upper[0] || current_price < b_lower[0])) return \"Trending_Era\";\n";
    code += " else if (band_width < ranging_threshold && MathAbs(current_price - b_mid[0]) < atr_val[0]) return \"Ranging_Era\";\n";
    code += " else if (atr_val[0] > SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 50) return \"Volatile_Era\";\n";
    code += " return \"Consolidation_Era\";\n";
    code += "}\n\n";

    code += "double CalculateSedimentDensity(int layer)\n";
    code += "{\n";
    code += " if (layer < 0 || layer >= Bars(_Symbol, _Period)) return 0.0;\n";
    code += " double vol_data[1];\n";
    code += " if (CopyBuffer(handle_volume, 0, layer, 1, vol_data) <= 0) return 0.0;\n";
    code += " return vol_data[0];\n";
    code += "}\n\n";

    code += "void MonitorPatternCrystallization()\n";
    code += "{\n";
    code += " static datetime last_check_time = 0;\n";
    code += " static int last_diamond_count = 0;\n";
    code += " datetime current_time = TimeCurrent();\n";
    code += " if (last_check_time == 0)\n";
    code += " {\n";
    code += " last_check_time = current_time;\n";
    code += " last_diamond_count = diamond_count;\n";
    code += " return;\n";
    code += " }\n";
    code += " long time_diff_sec = current_time - last_check_time;\n";
    code += " if (time_diff_sec == 0) return;\n";
    code += " int new_diamonds_found = diamond_count - last_diamond_count;\n";
    code += " pattern_crystallization_rate = (double)new_diamonds_found / (time_diff_sec / 3600.0);\n";
    code += " last_check_time = current_time;\n";
    code += " last_diamond_count = diamond_count;\n";
    code += "}\n\n";

    code += "string ExtractDiscoveryMethod(string pattern_type)\n";
    code += "{\n";
    code += " if (StringFind(pattern_type, \"Harmonic\") >= 0) return \"Quantum_Harmonics\";\n";
    code += " if (StringFind(pattern_type, \"Trend\") >= 0) return \"Seismic_Trend_Analysis\";\n";
    code += " if (StringFind(pattern_type, \"Range\") >= 0) return \"Volumetric_Consolidation\";\n";
    code += " return \"General_Archaeological_Survey\";\n";
    code += "}\n\n";

    code += "double GetLastSeismicAmplitude()\n";
    code += "{\n";
    code += " if (seismic_readings == 0) return 0.0;\n";
    code += " return seismic_data[seismic_readings - 1].price_change;\n";
    code += "}\n\n";

    code += "double GetLastRadarReflection()\n";
    code += "{\n";
    code += " double atr_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return 0.0;\n";
    code += " return atr_val[0] / SymbolInfoDouble(_Symbol, SYMBOL_POINT);\n";
    code += "}\n\n";

    code += "double GetLastQuantumSignature()\n";
    code += "{\n";
    code += " double rsi_val[1], macd_main[1], macd_signal[1], momentum_val[1];\n";
    code += " if (CopyBuffer(handle_rsi, 0, 0, 1, rsi_val) <= 0 ||\n";
    code += " CopyBuffer(handle_macd, 0, 0, 1, macd_main) <= 0 ||\n";
    code += " CopyBuffer(handle_macd, 1, 0, 1, macd_signal) <= 0 ||\n";
    code += " CopyBuffer(handle_momentum, 0, 0, 1, momentum_val) <= 0) return 0.0;\n";
    code += " return (rsi_val[0] * 0.1) + (macd_main[0] - macd_signal[0]) + (momentum_val[0] * 0.01);\n";
    code += "}\n\n";

    code += "string GetCurrentMarketSession()\n";
    code += "{\n";
    code += " MqlDateTime dt;\n";
    code += " TimeToStruct(TimeCurrent(), dt);\n";
    code += " int hour = dt.hour;\n";
    code += " if (hour >= 0 && hour < 9) return \"Asian_Session\";\n";
    code += " if (hour >= 8 && hour < 17) return \"European_Session\";\n";
    code += " if (hour >= 13 && hour < 22) return \"American_Session\";\n";
    code += " return \"Overlap_Session\";\n";
    code += "}\n\n";

    code += "string GetCurrentStratigraphicEra()\n";
    code += "{\n";
    code += " double b_upper[1], b_lower[1], b_mid[1];\n";
    code += " double atr_val[1];\n";
    code += " double rsi_val[1];\n";
    code += " double macd_main[1], macd_signal[1];\n";
    code += " if (CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, b_upper) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, b_lower) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, b_mid) <= 0 ||\n";
    code += " CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0 ||\n";
    code += " CopyBuffer(handle_rsi, 0, 0, 1, rsi_val) <= 0 ||\n";
    code += " CopyBuffer(handle_macd, 0, 0, 1, macd_main) <= 0 ||\n";
    code += " CopyBuffer(handle_macd, 1, 0, 1, macd_signal) <= 0) return \"Unknown_Stratum\";\n";
    code += " double band_width = b_upper[0] - b_lower[0];\n";
    code += " double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);\n";
    code += " double macd_hist = macd_main[0] - macd_signal[0];\n";
    code += " double high_volatility_threshold = atr_val[0] * 2.5;\n";
    code += " double low_volatility_threshold = atr_val[0] * 0.8;\n";
    code += " if (atr_val[0] > high_volatility_threshold)\n";
    code += " {\n";
    code += " if (current_price > b_upper[0] && macd_hist > 0) return \"Turbulent_Uptrend_Stratum\";\n";
    code =+ " if (current_price < b_lower[0] && macd_hist < 0) return \"Turbulent_Downtrend_Stratum\";\n";
    code += " return \"High_Volatility_Consolidation\";\n";
    code += " }\n";
    code += " else if (atr_val[0] < low_volatility_threshold)\n";
    code += " {\n";
    code += " if (rsi_val[0] > 70 || rsi_val[0] < 30) return \"Quiet_Extreme_Stratum\";\n";
    code += " return \"Low_Volatility_Sedimentary\";\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " if (macd_hist > 0 && rsi_val[0] > 50) return \"Stable_Uptrend_Formation\";\n";
    code += " if (macd_hist < 0 && rsi_val[0] < 50) return \"Stable_Downtrend_Formation\";\n";
    code += " return \"Transitional_Equilibrium\";\n";
    code += " }\n";
    code += "}\n\n";

    code += "string GetCurrentWeatherConditions()\n";
    code += "{\n";
    code += " double atr_val[1], momentum_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0 ||\n";
    code += " CopyBuffer(handle_momentum, 0, 0, 1, momentum_val) <= 0) return \"Cloudy\";\n";
    code += " double point_value = SymbolInfoDouble(_Symbol, SYMBOL_POINT);\n";
    code += " if (atr_val[0] > 50 * point_value && MathAbs(momentum_val[0]) > 0.001) return \"Stormy_Trend\";\n";
    code += " if (atr_val[0] < 10 * point_value && MathAbs(momentum_val[0]) < 0.0001) return \"Calm_Doldrums\";\n";
    code += " if (MathAbs(momentum_val[0]) > 0.0005) return \"Windy_Conditions\";\n";
    code += " return \"Mild_Breeze\";\n";
    code += "}\n\n";

    code += "double CalculateCurrentSedimentDensity()\n";
    code += "{\n";
    code += " return CalculateSedimentDensity(0);\n";
    code += "}\n\n";

    code += "double CalculateCurrentExcavationProbability()\n";
    code += "{\n";
    code += " return CalculateExcavationProbability(0);\n";
    code += "}\n\n";

    code += "bool DetectDiamondFormation(double amplitude, double volume_spike)\n";
    code += "{\n";
    code += " double atr_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return false;\n";
    code += " double current_vol[1];\n";
    code += " if (CopyBuffer(handle_volume, 0, 0, 1, current_vol) <= 0) return false;\n";
    code += " double min_amplitude = SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10;\n";
    code += " double average_volume = 0.0;\n";
    code += " long vol_sum = 0;\n";
    code += " int bars_to_check = MathMin(Bars(_Symbol, _Period), 20);\n";
    code += " double recent_volumes[20];\n";
    code += " if (CopyBuffer(handle_volume, 0, 1, bars_to_check, recent_volumes) > 0)\n";
    code += " {\n";
    code += " for (int i = 0; i < bars_to_check; i++) vol_sum += recent_volumes[i];\n";
    code += " average_volume = (double)vol_sum / bars_to_check;\n";
    code += " }\n";
    code += " bool significant_amplitude = amplitude > min_amplitude;\n";
    code += " bool significant_volume = current_vol[0] > average_volume * 1.5;\n";
    code += " double b_upper[1], b_lower[1], b_mid[1];\n";
    code += " if (CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, b_upper) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, b_lower) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, b_mid) <= 0)\n";
    code += " {\n";
    code += " return significant_amplitude && significant_volume;\n";
    code += " }\n";
    code += " double band_width = b_upper[0] - b_lower[0];\n";
    code += " double avg_band_width = 0.0;\n";
    code += " double b_upper_hist[20], b_lower_hist[20];\n";
    code += " if (CopyBuffer(handle_bbands, MODE_UPPER, 1, bars_to_check, b_upper_hist) > 0 &&\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 1, bars_to_check, b_lower_hist) > 0)\n";
    code += " {\n";
    code += " for(int i = 0; i < bars_to_check; i++) avg_band_width += (b_upper_hist[i] - b_lower_hist[i]);\n";
    code += " avg_band_width /= bars_to_check;\n";
    code += " }\n";
    code += " bool is_expanding = band_width > avg_band_width * 1.2;\n";
    code += " bool is_squeezing = band_width < avg_band_width * 0.8;\n";
    code += " return (significant_amplitude && significant_volume) || (is_expanding && significant_volume);\n";
    code += "}\n\n";

    code += "double CalculateBollingerPosition()\n";
    code += "{\n";
    code += " double b_upper[1], b_lower[1], b_mid[1];\n";
    code += " if (CopyBuffer(handle_bbands, MODE_UPPER, 0, 1, b_upper) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_LOWER, 0, 1, b_lower) <= 0 ||\n";
    code += " CopyBuffer(handle_bbands, MODE_MAIN, 0, 1, b_mid) <= 0) return 0.0;\n";
    code += " double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);\n";
    code += " double band_range = b_upper[0] - b_lower[0];\n";
    code += " if (band_range == 0) return 0.0;\n";
    code += " return (current_price - b_mid[0]) / (band_range / 2.0);\n";
    code += "}\n\n";

    code += "string ClassifyPatternType(double rsi_val, double macd_hist, double bb_upper_val, double bb_lower_val)\n";
    code += "{\n";
    code += " double current_price = SymbolInfoDouble(_Symbol, SYMBOL_LAST);\n";
    code += " if (rsi_val > 70.0)\n";
    code += " {\n";
    code += " if (current_price > bb_upper_val) return \"Overbought_Extreme\";\n";
    code += " return \"Overbought_Condition\";\n";
    code += " }\n";
    code += " if (rsi_val < 30.0)\n";
    code += " {\n";
    code += " if (current_price < bb_lower_val) return \"Oversold_Extreme\";\n";
    code += " return \"Oversold_Condition\";\n";
    code += " }\n";
    code += " if (macd_hist > 0)\n";
    code += " {\n";
    code += " if (current_price > bb_upper_val) return \"Bullish_Breakout\";\n";
    code += " return \"Bullish_Trend_Continuation\";\n";
    code += " }\n";
    code += " if (macd_hist < 0)\n";
    code += " {\n";
    code += " if (current_price < bb_lower_val) return \"Bearish_Breakout\";\n";
    code += " return \"Bearish_Trend_Continuation\";\n";
    code += " }\n";
    code += " if (current_price < bb_upper_val && current_price > bb_lower_val) return \"Range_Bound_Consolidation\";\n";
    code += " return \"Unclassified_Pattern\";\n";
    code += "}\n\n";

    code += "double ArraySum(double &arr[])\n";
    code += "{\n";
    code += " double sum = 0.0;\n";
    code += " for (int i = 0; i < ArraySize(arr); i++) sum += arr[i];\n";
    code += " return sum;\n";
    code += "}\n\n";

    code += "void CreateExcavationReportHeader()\n";
    code += "{\n";
    code += " int file_handle = FileOpen(ExcavationReport, FILE_WRITE | FILE_CSV | FILE_ANSI, ',');\n";
    code += " if (file_handle != INVALID_HANDLE)\n";
    code += " {\n";
    code += " FileWrite(file_handle, \"Time Discovered\", \"Pattern Type\", \"Clarity Score\", \"Carat Weight\", \"Cut Quality\", \"Color Grade\", \"Formation Depth\", \"Extraction Confidence (%)\", \"Geological Layer\", \"Resonance Frequency\", \"Crystalline Structure\", \"Refraction Index\");\n";
    code += " FileClose(file_handle);\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"ERROR: Failed to create excavation report header. Error: \", GetLastError());\n";
    code += " }\n";
    code += "}\n\n";

    code += "void LogDiamondDiscovery(DiamondPattern& diamond)\n";
    code += "{\n";
    code += " int file_handle = FileOpen(ExcavationReport, FILE_READ | FILE_WRITE | FILE_CSV | FILE_ANSI, ',');\n";
    code += " if (file_handle != INVALID_HANDLE)\n";
    code += " {\n";
    code += " FileSeek(file_handle, 0, SEEK_END);\n";
    code += " FileWrite(file_handle, TimeToString(diamond.discovered_time, TIME_DATE|TIME_SECONDS), diamond.pattern_type, DoubleToString(diamond.clarity_score, 2), DoubleToString(diamond.carat_weight, 4), diamond.cut_quality, diamond.color_grade, IntegerToString(diamond.formation_depth), DoubleToString(diamond.extraction_confidence, 2), diamond.geological_layer, DoubleToString(diamond.resonance_frequency, 4), diamond.crystalline_structure, DoubleToString(diamond.refraction_index, 4));\n";
    code += " FileClose(file_handle);\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"ERROR: Failed to log diamond discovery. Error: \", GetLastError());\n";
    code += " }\n";
    code += "}\n\n";

    code += "void UpdatePatternHeatMap(DiamondPattern& diamond)\n";
    code += "{\n";
    code += " MqlDateTime dt;\n";
    code += " TimeToStruct(diamond.discovered_time, dt);\n";
    code += " int day_of_week = dt.day_of_week;\n";
    code += " int hour = dt.hour;\n";
    code += " if (day_of_week >= 0 && day_of_week <= 6 && hour >= 0 && hour <= 23)\n";
    code += " {\n";
    code += " pattern_heatmap[day_of_week][hour]++;\n";
    code += " }\n";
    code += "}\n\n";

    code += "void GenerateFinalExcavationReport()\n";
    code += "{\n";
    code += " int report_file = FileOpen(PatternMap, FILE_WRITE | FILE_ANSI);\n";
    code += " if (report_file != INVALID_HANDLE)\n";
    code += " {\n";
    code += " FileWrite(report_file, \"--- DIAMOND EXCAVATION SUMMARY REPORT ---\");\n";
    code += " FileWrite(report_file, \"Generated: \" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));\n";
    code += " FileWrite(report_file, \"Total Diamonds Discovered: \" + IntegerToString(diamond_count));\n";
    code += " FileWrite(report_file, \"Average Clarity Score: \" + DoubleToString(CalculateAverageClarity(), 2));\n";
    code += " FileWrite(report_file, \"Average Carat Weight: \" + DoubleToString(CalculateAverageCaratWeight(), 4));\n";
    code += " FileWrite(report_file, \"Dominant Pattern Type: \" + GetDominantPattern());\n";
    code += " FileWrite(report_file, \"Highest Clarity Diamond: \" + DoubleToString(GetHighestClarity(), 2));\n";
    code += " FileWrite(report_file, \"Heaviest Carat Diamond: \" + DoubleToString(GetHeaviestCarat(), 4));\n";
    code += " FileWrite(report_file, \"\\n--- Pattern Heatmap (Discoveries by Day/Hour) ---\");\n";
    code += " FileWrite(report_file, \" 00 01 02 03 04 05 06 07 08 09 10 11 12 13 14 15 16 17 18 19 20 21 22 23\");\n";
    code += " string days[] = {\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"};\n";
    code += " for (int d = 0; d < 7; d++)\n";
    code += " {\n";
    code += " string line = days[d] + \": \";\n";
    code += " for (int h = 0; h < 24; h++)\n";
    code += " {\n";
    code += " line += IntegerToString((int)pattern_heatmap[d][h]) + (pattern_heatmap[d][h] < 10 ? \" \" : \" \");\n";
    code += " }\n";
    code += " FileWrite(report_file, line);\n";
    code += " }\n";
    code += " FileWrite(report_file, \"------------------------------------------\");\n";
    code += " FileClose(report_file);\n";
    code += " }\n";
    code += " else\n";
    code += " {\n";
    code += " Print(\"ERROR: Failed to generate final excavation report. Error: \", GetLastError());\n";
    code += " }\n";
    code += "}\n\n";

    code += "double CalculateAverageClarity()\n";
    code += "{\n";
    code += " if (diamond_count == 0) return 0.0;\n";
    code += " double total_clarity = 0.0;\n";
    code += " for (int i = 0; i < diamond_count; i++) total_clarity += discovered_diamonds[i].clarity_score;\n";
    code += " return total_clarity / diamond_count;\n";
    code += "}\n\n";

    code += "double CalculateAverageCaratWeight()\n";
    code += "{\n";
    code += " if (diamond_count == 0) return 0.0;\n";
    code += " double total_carat = 0.0;\n";
    code += " for (int i = 0; i < diamond_count; i++) total_carat += discovered_diamonds[i].carat_weight;\n";
    code += " return total_carat / diamond_count;\n";
    code += "}\n\n";

    code += "string GetDominantPattern()\n";
    code += "{\n";
    code += " if (diamond_count == 0) return \"No_Dominant_Pattern_Yet\";\n";
    code += " string patterns[];\n";
    code += " int counts[];\n";
    code += " int unique_patterns = 0;\n";
    code += " for (int i = 0; i < diamond_count; i++)\n";
    code += " {\n";
    code += " string current_pattern = discovered_diamonds[i].pattern_type;\n";
    code += " bool found = false;\n";
    code += " for (int j = 0; j < unique_patterns; j++)\n";
    code += " {\n";
    code += " if (patterns[j] == current_pattern) { counts[j]++; found = true; break; }\n";
    code += " }\n";
    code += " if (!found)\n";
    code += " {\n";
    code += " ArrayResize(patterns, unique_patterns + 1);\n";
    code += " ArrayResize(counts, unique_patterns + 1);\n";
    code += " patterns[unique_patterns] = current_pattern;\n";
    code += " counts[unique_patterns] = 1;\n";
    code += " unique_patterns++;\n";
    code += " }\n";
    code += " }\n";
    code += " if (unique_patterns == 0) return \"No_Dominant_Pattern_Yet\";\n";
    code += " int max_count = 0;\n";
    code += " string dominant = \"No_Dominant_Pattern_Yet\";\n";
    code += " for (int i = 0; i < unique_patterns; i++)\n";
    code += " {\n";
    code += " if (counts[i] > max_count) { max_count = counts[i]; dominant = patterns[i]; }\n";
    code += " }\n";
    code += " return dominant;\n";
    code += "}\n\n";

    code += "double GetHighestClarity()\n";
    code += "{\n";
    code += " if (diamond_count == 0) return 0.0;\n";
    code += " double max_clarity = 0.0;\n";
    code += " for (int i = 0; i < diamond_count; i++) { if (discovered_diamonds[i].clarity_score > max_clarity) max_clarity = discovered_diamonds[i].clarity_score; }\n";
    code += " return max_clarity;\n";
    code += "}\n\n";

    code += "double GetHeaviestCarat()\n";
    code += "{\n";
    code += " if (diamond_count == 0) return 0.0;\n";
    code += " double max_carat = 0.0;\n";
    code += " for (int i = 0; i < diamond_count; i++) { if (discovered_diamonds[i].carat_weight > max_carat) max_carat = discovered_diamonds[i].carat_weight; }\n";
    code += " return max_carat;\n";
    code += "}\n\n";

    code += "double CalculateTickIntensity()\n";
    code += "{\n";
    code += " static datetime last_tick_time = 0;\n";
    code += " datetime current_time = TimeCurrent();\n";
    code += " if (last_tick_time == 0) { last_tick_time = current_time; return 0.0; }\n";
    code += " double time_diff_sec = (double)(current_time - last_tick_time);\n";
    code += " last_tick_time = current_time;\n";
    code += " if (time_diff_sec == 0) return 100.0;\n";
    code += " return 1.0 / time_diff_sec;\n";
    code += "}\n\n";

    code += "double ScanForHiddenStructures(int frequency)\n";
    code += "{\n";
    code += " if (frequency <= 0) return 0.0;\n";
    code += " double swing_amplitude = 0.0;\n";
    code += " double avg_volume_recent = 0.0;\n";
    code += " MqlRates rates[];\n";
    code += " int count = CopyRates(_Symbol, _Period, 0, frequency + 1, rates);\n";
    code += " if (count > 1)\n";
    code += " {\n";
    code += " double max_price = rates[0].high;\n";
    code += " double min_price = rates[0].low;\n";
    code += " for (int i = 0; i < count; i++) { if (rates[i].high > max_price) max_price = rates[i].high; if (rates[i].low < min_price) min_price = rates[i].low; avg_volume_recent += rates[i].tick_volume; }\n";
    code += " swing_amplitude = (max_price - min_price) / SymbolInfoDouble(_Symbol, SYMBOL_POINT);\n";
    code += " avg_volume_recent /= count;\n";
    code += " }\n";
    code += " return (swing_amplitude * avg_volume_recent) / frequency;\n";
    code += "}\n\n";

    // --- FULL HARMONIC DETECTION IMPLEMENTATION ---
    // These functions are now fully detailed, including Fibonacci ratio checks.
    // They are critical for the 'Quantum_Harmonics' discovery method.

    code += "bool Harmonic_IsApproxEqual(double value, double target, double tolerance)\n";
    code += "{\n";
    code += " return MathAbs(value - target) <= tolerance;\n";
    code += "}\n\n";

    code += "double Harmonic_CalculateRatio(double p1, double p2, double p3)\n";
    code += "{\n";
    code += " if (MathAbs(p1 - p2) < DBL_EPSILON) return 0.0;\n";
    code += " return MathAbs(p3 - p2) / MathAbs(p1 - p2);\n";
    code += "}\n\n";

    code += "double Harmonic_CalculateExtension(double p1, double p2, double p3)\n";
    code += "{\n";
    code += " if (MathAbs(p2 - p3) < DBL_EPSILON) return 0.0;\n";
    code += " return MathAbs(p3 - p1) / MathAbs(p2 - p3);\n";
    code += "}\n\n";

    code += "string Harmonic_GetPatternDirection(double x, double a)\n";
    code += "{\n";
    code += " if (a > x) return \"Bullish\";\n";
    code += " if (a < x) return \"Bearish\";\n";
    code += " return \"Neutral\";\n";
    code += "}\n\n";

    code += "bool Harmonic_CheckGartley(double x, double a, double b, double c, double d)\n";
    code += "{\n";
    code += " double tolerance = 0.05; // 5% tolerance for Fibonacci ratios\n";
    code += " string direction = Harmonic_GetPatternDirection(x, a);\n";
    code += " if (direction == \"Neutral\") return false;\n";
    code += " \n";
    code += " // XA leg (base)\n";
    code += " // AB = 0.618 XA\n";
    code += " double ab_xa = Harmonic_CalculateRatio(x, a, b);\n";
    code += " if (!Harmonic_IsApproxEqual(ab_xa, 0.618, tolerance)) return false;\n";
    code += " \n";
    code += " // BC = 0.382 - 0.886 AB\n";
    code += " double bc_ab = Harmonic_CalculateRatio(a, b, c);\n";
    code += " if (!(bc_ab >= 0.382 - tolerance && bc_ab <= 0.886 + tolerance)) return false;\n";
    code += " \n";
    code += " // CD = 1.13 - 1.618 BC AND D is 0.786 XA\n";
    code += " double cd_bc = Harmonic_CalculateRatio(b, c, d);\n";
    code += " double d_xa = Harmonic_CalculateRatio(x, a, d);\n"; // Recalculated D point relative to XA
    code += " \n";
    code += " // Check the CD leg and the D point against XA\n";
    code += " bool cd_ratio_ok = (cd_bc >= 1.13 - tolerance && cd_bc <= 1.618 + tolerance);\n";
    code += " bool d_xa_ratio_ok = Harmonic_IsApproxEqual(d_xa, 0.786, tolerance);\n";
    code += " \n";
    code += " return cd_ratio_ok && d_xa_ratio_ok;\n";
    code += "}\n\n";

    code += "bool Harmonic_CheckBat(double x, double a, double b, double c, double d)\n";
    code += "{\n";
    code += " double tolerance = 0.05;\n";
    code += " string direction = Harmonic_GetPatternDirection(x, a);\n";
    code += " if (direction == \"Neutral\") return false;\n";
    code += " \n";
    code += " // AB = 0.382 - 0.50 XA\n";
    code += " double ab_xa = Harmonic_CalculateRatio(x, a, b);\n";
    code += " if (!(ab_xa >= 0.382 - tolerance && ab_xa <= 0.50 + tolerance)) return false;\n";
    code += " \n";
    code += " // BC = 0.382 - 0.886 AB\n";
    code += " double bc_ab = Harmonic_CalculateRatio(a, b, c);\n";
    code += " if (!(bc_ab >= 0.382 - tolerance && bc_ab <= 0.886 + tolerance)) return false;\n";
    code += " \n";
    code += " // CD = 1.618 - 2.618 BC AND D is 0.886 XA\n";
    code += " double cd_bc = Harmonic_CalculateRatio(b, c, d);\n";
    code += " double d_xa = Harmonic_CalculateRatio(x, a, d);\n";
    code += " \n";
    code += " bool cd_ratio_ok = (cd_bc >= 1.618 - tolerance && cd_bc <= 2.618 + tolerance);\n";
    code += " bool d_xa_ratio_ok = Harmonic_IsApproxEqual(d_xa, 0.886, tolerance);\n";
    code += " \n";
    code += " return cd_ratio_ok && d_xa_ratio_ok;\n";
    code += "}\n\n";

    code += "bool Harmonic_CheckButterfly(double x, double a, double b, double c, double d)\n";
    code += "{\n";
    code += " double tolerance = 0.05;\n";
    code += " string direction = Harmonic_GetPatternDirection(x, a);\n";
    code += " if (direction == \"Neutral\") return false;\n";
    code += " \n";
    code += " // AB = 0.786 XA\n";
    code += " double ab_xa = Harmonic_CalculateRatio(x, a, b);\n";
    code += " if (!Harmonic_IsApproxEqual(ab_xa, 0.786, tolerance)) return false;\n";
    code += " \n";
    code += " // BC = 0.382 - 0.886 AB\n";
    code += " double bc_ab = Harmonic_CalculateRatio(a, b, c);\n";
    code += " if (!(bc_ab >= 0.382 - tolerance && bc_ab <= 0.886 + tolerance)) return false;\n";
    code += " \n";
    code += " // CD = 1.618 - 2.618 BC AND D is 1.27 or 1.618 XA extension\n";
    code += " double cd_bc = Harmonic_CalculateRatio(b, c, d);\n";
    code += " double d_xa_ext = Harmonic_CalculateExtension(x, a, d);\n"; // This calculation needs to be correct for extension\n";
    code += " \n";
    code += " bool cd_ratio_ok = (cd_bc >= 1.618 - tolerance && cd_bc <= 2.618 + tolerance);\n";
    code += " bool d_xa_ext_ok = (Harmonic_IsApproxEqual(d_xa_ext, 1.27, tolerance) || Harmonic_IsApproxEqual(d_xa_ext, 1.618, tolerance));\n";
    code += " \n";
    code += " return cd_ratio_ok && d_xa_ext_ok;\n";
    code += "}\n\n";

    code += "bool Harmonic_CheckCrab(double x, double a, double b, double c, double d)\n";
    code += "{\n";
    code += " double tolerance = 0.05;\n";
    code += " string direction = Harmonic_GetPatternDirection(x, a);\n";
    code += " if (direction == \"Neutral\") return false;\n";
    code += " \n";
    code += " // AB = 0.382 - 0.618 XA\n";
    code += " double ab_xa = Harmonic_CalculateRatio(x, a, b);\n";
    code += " if (!(ab_xa >= 0.382 - tolerance && ab_xa <= 0.618 + tolerance)) return false;\n";
    code += " \n";
    code += " // BC = 0.382 - 0.886 AB\n";
    code += " double bc_ab = Harmonic_CalculateRatio(a, b, c);\n";
    code += " if (!(bc_ab >= 0.382 - tolerance && bc_ab <= 0.886 + tolerance)) return false;\n";
    code += " \n";
    code += " // CD = 2.24 - 3.618 BC AND D is 1.618 XA extension\n";
    code += " double cd_bc = Harmonic_CalculateRatio(b, c, d);\n";
    code += " double d_xa_ext = Harmonic_CalculateExtension(x, a, d);\n"; // This calculation needs to be correct for extension\n";
    code += " \n";
    code += " bool cd_ratio_ok = (cd_bc >= 2.24 - tolerance && cd_bc <= 3.618 + tolerance);\n";
    code += " bool d_xa_ext_ok = Harmonic_IsApproxEqual(d_xa_ext, 1.618, tolerance);\n";
    code += " \n";
    code += " return cd_ratio_ok && d_xa_ext_ok;\n";
    code += "}\n\n";

    code += "bool Harmonic_CheckABCD(double a, double b, double c, double d)\n";
    code += "{\n";
    code += " double tolerance = 0.05;\n";
    code += " // AB=CD\n";
    code += " bool ab_eq_cd = Harmonic_IsApproxEqual(MathAbs(b - a), MathAbs(d - c), tolerance * MathAbs(b-a));\n";
    code += " if (!ab_eq_cd) return false;\n";
    code += " \n";
    code += " // BC retracement of AB (common ratios are 0.382, 0.50, 0.618, 0.786, 0.886)\n";
    code += " double bc_ab = Harmonic_CalculateRatio(a, b, c);\n";
    code += " if (!( (bc_ab >= 0.382 - tolerance && bc_ab <= 0.382 + tolerance) ||\n";
    code += " (bc_ab >= 0.50 - tolerance && bc_ab <= 0.50 + tolerance) ||\n";
    code += " (bc_ab >= 0.618 - tolerance && bc_ab <= 0.618 + tolerance) ||\n";
    code += " (bc_ab >= 0.786 - tolerance && bc_ab <= 0.786 + tolerance) ||\n";
    code += " (bc_ab >= 0.886 - tolerance && bc_ab <= 0.886 + tolerance) ) ) return false;\n";
    code += " \n";
    code += " // Additional condition: A, B, C, D must form a clear swing sequence (e.g., higher high/lower low or vice versa)\n";
    code += " // For simplicity, we assume the input 'points' already represent valid swing points.\n";
    code += " \n";
    code += " return true;\n";
    code += "}\n\n";

    code += "string Harmonic_DetectPatterns(const double& points[])\n";
    code += "{\n";
    code += " if (ArraySize(points) < 4) return \"Not_Enough_Points_For_Harmonic\";\n";
    code += " \n";
    code += " double x = points[0];\n";
    code += " double a = points[1];\n";
    code += " double b = points[2];\n";
    code += " double c = points[3];\n";
    code += " double d = points[4];\n"; // Assuming 5 points for XABCD patterns\n";
    code += " \n";
    code += " // Check for ABCD pattern first, as it's simpler and fundamental\n";
    code += " if (Harmonic_CheckABCD(a, b, c, d)) { return \"Harmonic_ABCD\"; }\n";
    code += " \n";
    code += " // Check for Gartley pattern\n";
    code += " if (Harmonic_CheckGartley(x, a, b, c, d))\n";
    code += " {\n";
    code += " if (Harmonic_GetPatternDirection(x, a) == \"Bullish\") return \"Harmonic_Gartley_Bullish_Reversal\";\n";
    code += " else return \"Harmonic_Gartley_Bearish_Reversal\";\n";
    code += " }\n";
    code += " \n";
    code += " // Check for Bat pattern\n";
    code += " if (Harmonic_CheckBat(x, a, b, c, d))\n";
    code += " {\n";
    code += " if (Harmonic_GetPatternDirection(x, a) == \"Bullish\") return \"Harmonic_Bat_Bullish_Reversal\";\n";
    code += " else return \"Harmonic_Bat_Bearish_Reversal\";\n";
    code += " }\n";
    code += " \n";
    code += " // Check for Butterfly pattern\n";
    code += " if (Harmonic_CheckButterfly(x, a, b, c, d))\n";
    code += " {\n";
    code += " if (Harmonic_GetPatternDirection(x, a) == \"Bullish\") return \"Harmonic_Butterfly_Bullish_Reversal\";\n";
    code += " else return \"Harmonic_Butterfly_Bearish_Reversal\";\n";
    code += " }\n";
    code += " \n";
    code += " // Check for Crab pattern\n";
    code += " if (Harmonic_CheckCrab(x, a, b, c, d))\n";
    code += " {\n";
    code += " if (Harmonic_GetPatternDirection(x, a) == \"Bullish\") return \"Harmonic_Crab_Bullish_Reversal\";\n";
    code += " else return \"Harmonic_Crab_Bearish_Reversal\";\n";
    code += " }\n";
    code += " \n";
    code += " return \"No_Harmonic_Detected_Yet\";\n";
    code += "}\n\n";
    // --- END FULL HARMONIC DETECTION IMPLEMENTATION ---

    code += "double CalculateSoilHardness(int depth)\n";
    code += "{\n";
    code += " double atr_val[1], volume_avg[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, depth, 1, atr_val) <= 0 ||\n";
    code += " CopyBuffer(handle_volume, 0, depth, 1, volume_avg) <= 0) return 0.0;\n";
    code += " return (volume_avg[0] / 1000.0) / (atr_val[0] + 0.00001);\n";
    code += "}\n\n";

    code += "double CalculateWaterTableFactor(int depth)\n";
    code += "{\n";
    code += " double volume_data[1];\n";
    code += " if (CopyBuffer(handle_volume, 0, depth, 1, volume_data) <= 0) return 1.0;\n";
    code += " double current_spread = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID);\n";
    code += " return current_spread / SymbolInfoDouble(_Symbol, SYMBOL_POINT) / (volume_data[0] + 1.0);\n";
    code += "}\n\n";

    code += "double CalculateGeologicalStability(int depth)\n";
    code += "{\n";
    code += " double atr_val[1], momentum_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, depth, 1, atr_val) <= 0 ||\n";
    code += " CopyBuffer(handle_momentum, 0, depth, 1, momentum_val) <= 0) return 0.0;\n";
    code += " double normalized_atr = atr_val[0] / (SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 100);\n";
    code += " double normalized_momentum = MathAbs(momentum_val[0]) / 0.01;\n";
    code += " return 1.0 - MathMin(1.0, normalized_atr + normalized_momentum);\n";
    code += "}\n\n";

    code += "double CalculateWeatherFactor()\n";
    code += "{\n";
    code += " double atr_val[1];\n";
    code += " if (CopyBuffer(handle_atr, 0, 0, 1, atr_val) <= 0) return 1.0;\n";
    code += " double point_value = SymbolInfoDouble(_Symbol, SYMBOL_POINT);\n";
    code += " double normalized_atr = atr_val[0] / (point_value * 50);\n";
    code += " string session = GetCurrentMarketSession();\n";
    code += " double session_factor = 1.0;\n";
    code += " if (session == \"Asian_Session\") session_factor = 0.8;\n";
    code += " else if (session == \"European_Session\" || session == \"American_Session\") session_factor = 1.2;\n";
    code += " else if (session == \"Overlap_Session\") session_factor = 1.5;\n";
    code += " return normalized_atr * session_factor;\n";
    code += "}\n\n";

    code += "double CalculateTimeOfDayFactor()\n";
    code += "{\n";
    code += " MqlDateTime dt;\n";
    code += " TimeToStruct(TimeCurrent(), dt);\n";
    code += " int hour = dt.hour;\n";
    code += " if (hour >= 8 && hour < 10) return 1.2;\n";
    code += " if (hour >= 13 && hour < 16) return 1.5;\n";
    code += " if (hour >= 10 && hour < 13) return 1.0;\n";
    code += " if (hour >= 20 || hour < 5) return 0.7;\n";
    code += " return 0.8;\n";
    code += "}\n\n";

    code += "double CalculateEquipmentEfficiency()\n";
    code += "{\n";
    code += " double current_spread = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID);\n";
    code += " double avg_spread = SymbolInfoDouble(_Symbol, SYMBOL_SPREAD_FLOAT);\n";
    code += " if (avg_spread == 0) return 1.0;\n";
    code += " return MathMax(0.1, 1.0 - (current_spread / avg_spread) * 0.5);\n";
    code += "}\n\n";

    code += "double CalculatePatternClarity(double rsi_val, double macd_hist)\n";
    code += "{\n";
    code += " double clarity = 0.0;\n";
    code += " if (rsi_val > 70.0 || rsi_val < 30.0) clarity += 2.0;\n";
    code += " else if (rsi_val > 60.0 || rsi_val < 40.0) clarity += 1.0;\n";
    code += " else clarity += 0.5;\n";
    code += " if (MathAbs(macd_hist) > 0.0001) clarity += 2.0;\n";
    code += " else if (MathAbs(macd_hist) > 0.00005) clarity += 1.0;\n";
    code += " else clarity += 0.5;\n";
    code += " double bollinger_pos = CalculateBollingerPosition();\n";
    code += " if ((macd_hist > 0 && rsi_val < 50 && bollinger_pos < -0.5) ||\n";
    code += " (macd_hist < 0 && rsi_val > 50 && bollinger_pos > 0.5)) clarity += 1.5;\n";
    code += " else if ((macd_hist > 0 && rsi_val > 50 && bollinger_pos > 0) ||\n";
    code += " (macd_hist < 0 && rsi_val < 50 && bollinger_pos < 0)) clarity += 1.0;\n";
    code += " return MathMin(10.0, clarity);\n";
    code += "}\n";
    code += "//+------------------------------------------------------------------+\n"; // End of generated utility functions
    return code;
}

/*
// This part is for the original EA to call the reconstruction functions.
// It is not part of the generated EA but part of the EA that GENERATES the new EA.
// The actual calls to GenerateEAHeader(), GenerateInputParameters(), etc.
// happen within GenerateReconstructedEA() in the CURRENT running EA.
// The functions above are STRING GENERATORS.
*/