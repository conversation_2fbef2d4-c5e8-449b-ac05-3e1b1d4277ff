//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize all enhanced components
    g_spread_analyzer.Reset();
    g_performance_monitor = new PerformanceMonitor();
    g_latency_optimizer = new LatencyOptimizer(MaxAcceptableLatency);
    g_market_analysis = new MarketMicrostructure();
    g_position_sizer = new PositionSizer(LotSize, MaxPositionSizeMultiplier, g_performance_monitor);
    g_order_executor = new OrderExecutor(&trade, MagicNumber, g_latency_optimizer, ExecutionRetryAttempts, ExecutionRetryDelayMs);
    
    // Auto-detect broker timezone
    if(!DetectBrokerTimezone())
    {
        Print("ERROR: Failed to detect broker timezone. Please set BrokerTimezone manually.");
        return(INIT_FAILED);
    }
    
    // Validate and set dynamic tick/pip values
    if(!InitializeSymbolProperties())
    {
        Print("ERROR: Failed to initialize symbol properties.");
        return(INIT_FAILED);
    }
    
    // Set spread cost in pips
    g_spread_cost_pips = (int)SpreadCostPips;
    
    // Set trade parameters
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(MaxSlippagePoints);
    trade.SetTypeFilling(ORDER_FILLING_IOC); // Default filling mode
    
    // Validate input parameters
    if(!ValidateInputParameters())
    {
        Print("ERROR: Invalid input parameters detected.");
        return(INIT_FAILED);
    }
    
    // Initialize market data
    if(!UpdateMarketData())
    {
        Print("WARNING: Initial market data update failed");
    }
    
    Print("🚀 === Elite HFT Scalper BEAST MODE v3.0 ===");
    Print("Magic Number: ", MagicNumber);
    Print("Broker UTC Offset: ", g_broker_utc_offset, " hours");
    Print("📊 Enhanced Features Enabled:");
    Print("  ✅ Performance Monitoring & Analysis");
    Print("  ✅ Dynamic Position Sizing");
    Print("  ✅ Latency Optimization (Max: ", DoubleToString(MaxAcceptableLatency, 0), "ms)");
    Print("  ✅ Market Microstructure Analysis");
    Print("  ✅ Memory-Optimized Spread Analysis");
    Print("  ✅ Advanced Error Handling");
    Print("💰 Risk Management:");
    Print("  Daily Loss Limit: $", DoubleToString(DailyLossLimit, 2));
    Print("  Daily Profit Limit: $", DoubleToString(DailyProfitLimit, 2));
    Print("  Max Consecutive Losses: ", MaxConsecutiveLosses);
    Print("📈 Trading Parameters:");
    Print("  Base Lot Size: ", DoubleToString(LotSize, 2), " (Dynamic sizing: ", EnablePositionSizing ? "ON" : "OFF", ")");
    Print("  Max Position Multiplier: ", DoubleToString(MaxPositionSizeMultiplier, 1), "x");
    Print("  Tick Value: $", DoubleToString(g_tick_value, 4), " per tick");
    Print("  Pip Value: $", DoubleToString(g_pip_value, 4), " per pip");
    Print("  Spread Cost: ", g_spread_cost_pips, " pips ($", 
          DoubleToString(g_spread_cost_pips * g_pip_value, 3), ")");
    Print("  Target Profit: ", ProfitTarget, " pips ($", 
          DoubleToString(ProfitTarget * g_pip_value, 3), ")");
    Print("🏁 === BEAST MODE INITIALIZATION COMPLETE ===");
    
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Print comprehensive final statistics
    Print("🏁 === FINAL SESSION STATISTICS ===");
    
    if(g_performance_monitor != NULL)
    {
        g_performance_monitor.PrintDailyStats();
    }
    
    if(g_spread_analyzer.GetSampleCount() > 50)
    {
        g_spread_analyzer.PrintStatistics();
    }
    
    if(g_order_executor != NULL)
    {
        g_order_executor.PrintExecutionStats();
    }
    
    if(g_latency_optimizer != NULL)
    {
        g_latency_optimizer//+------------------------------------------------------------------+
//|                                Enhanced_EliteHFT_US30_Scalper.mq5 |
//|                     Elite HFT Scalping EA - BEAST MODE ENHANCED |
//|                    With Advanced Market Analysis & Optimization  |
//+------------------------------------------------------------------+
#property copyright "Elite HFT Scalper Beast Mode"
#property version   "3.0"
#property strict

#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

// Global objects
CTrade trade;
CPositionInfo position;
COrderInfo order;

// Input parameters
input double LotSize = 0.01;                    // Base lot size (will be optimized)
input int ProfitTarget = 10;                    // Profit target in pips (8-12 optimal range)
input int MaxSpread = 4;                        // Maximum allowed spread in pips (2.5 typical)
input double SpreadCostPips = 5.0;              // Total spread cost in pips (round trip)
input int MagicNumber = 777888;                 // Unique magic number
input bool EnableTrading = true;                // Master trading switch
input int MaxSlippagePoints = 10;               // Maximum slippage allowed
input bool EnableTimeFilter = true;             // Enable market hours protection
input int SpreadSpikeWaitSeconds = 5;           // Wait time after spread spike
input double OrderInsideSpreadPercent = 0.0;   // % inside spread for better fills (0-50)
input string BrokerTimezone = "AUTO";           // AUTO, UTC, GMT+2, GMT+3, etc.
input int ExecutionRetryAttempts = 3;           // Number of retry attempts for orders
input int ExecutionRetryDelayMs = 50;           // Delay between retry attempts (ms)
input double SpreadSpikeThreshold = 1.5;       // Multiplier for spread spike detection
input double DailyLossLimit = 100.0;           // Daily loss limit in account currency
input double DailyProfitLimit = 500.0;         // Daily profit limit in account currency
input int MaxConsecutiveLosses = 5;            // Max consecutive losses before pause
input bool EnablePositionSizing = true;        // Enable dynamic position sizing
input double MaxPositionSizeMultiplier = 2.0;  // Maximum position size multiplier
input bool EnableLatencyOptimization = true;   // Enable latency monitoring
input double MaxAcceptableLatency = 100.0;     // Maximum acceptable latency in ms

//+------------------------------------------------------------------+
//| Market Data Structure for Performance                           |
//+------------------------------------------------------------------+
struct MarketData
{
    double bid;
    double ask;
    double spread;
    double mid_price;
    datetime quote_time;
    long bid_volume;
    long ask_volume;
    bool valid;
    bool updated;
};

//+------------------------------------------------------------------+
//| Enhanced Performance Monitor Class                              |
//+------------------------------------------------------------------+
class PerformanceMonitor
{
private:
    struct TradeStats
    {
        int total_trades;
        int winning_trades;
        int losing_trades;
        double total_profit;
        double total_loss;
        double max_profit;
        double max_loss;
        double max_drawdown;
        double current_drawdown;
        double peak_balance;
        datetime first_trade;
        datetime last_trade;
        double daily_pnl;
        datetime last_daily_reset;
    };
    
    TradeStats m_stats;
    double m_trade_history[100]; // Last 100 trades for analysis
    int m_history_index;
    int m_consecutive_losses;
    int m_consecutive_wins;
    
public:
    PerformanceMonitor()
    {
        Reset();
        ArrayInitialize(m_trade_history, 0.0);
        m_history_index = 0;
    }
    
    void Reset()
    {
        ZeroMemory(m_stats);
        m_consecutive_losses = 0;
        m_consecutive_wins = 0;
        m_stats.peak_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        m_stats.last_daily_reset = TimeCurrent();
    }
    
    void UpdateStats(double trade_profit)
    {
        // Check for daily reset
        MqlDateTime current_dt, last_dt;
        TimeToStruct(TimeCurrent(), current_dt);
        TimeToStruct(m_stats.last_daily_reset, last_dt);
        
        if(current_dt.day != last_dt.day)
        {
            ResetDailyStats();
        }
        
        m_stats.total_trades++;
        m_stats.daily_pnl += trade_profit;
        
        // Add to history
        m_trade_history[m_history_index] = trade_profit;
        m_history_index = (m_history_index + 1) % 100;
        
        if(trade_profit > 0)
        {
            m_stats.winning_trades++;
            m_stats.total_profit += trade_profit;
            m_stats.max_profit = MathMax(m_stats.max_profit, trade_profit);
            m_consecutive_wins++;
            m_consecutive_losses = 0;
        }
        else if(trade_profit < 0)
        {
            m_stats.losing_trades++;
            m_stats.total_loss += MathAbs(trade_profit);
            m_stats.max_loss = MathMax(m_stats.max_loss, MathAbs(trade_profit));
            m_consecutive_losses++;
            m_consecutive_wins = 0;
        }
        
        // Update drawdown
        double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        if(current_balance > m_stats.peak_balance)
        {
            m_stats.peak_balance = current_balance;
            m_stats.current_drawdown = 0;
        }
        else
        {
            m_stats.current_drawdown = m_stats.peak_balance - current_balance;
            m_stats.max_drawdown = MathMax(m_stats.max_drawdown, m_stats.current_drawdown);
        }
        
        m_stats.last_trade = TimeCurrent();
        if(m_stats.first_trade == 0) m_stats.first_trade = TimeCurrent();
    }
    
    void ResetDailyStats()
    {
        Print("=== DAILY RESET ===");
        PrintDailyStats();
        m_stats.daily_pnl = 0;
        m_stats.last_daily_reset = TimeCurrent();
    }
    
    double GetWinRate() const
    {
        return (m_stats.total_trades > 0) ? (double)m_stats.winning_trades / m_stats.total_trades * 100.0 : 0.0;
    }
    
    double GetRecentWinRate(int lookback = 20) const
    {
        if(m_stats.total_trades < lookback) return GetWinRate();
        
        int wins = 0;
        int start_idx = (m_history_index - lookback + 100) % 100;
        
        for(int i = 0; i < lookback; i++)
        {
            int idx = (start_idx + i) % 100;
            if(m_trade_history[idx] > 0) wins++;
        }
        
        return (double)wins / lookback * 100.0;
    }
    
    double GetProfitFactor() const
    {
        return (m_stats.total_loss > 0) ? m_stats.total_profit / m_stats.total_loss : 0.0;
    }
    
    double GetAverageWin() const
    {
        return (m_stats.winning_trades > 0) ? m_stats.total_profit / m_stats.winning_trades : 0.0;
    }
    
    double GetAverageLoss() const
    {
        return (m_stats.losing_trades > 0) ? m_stats.total_loss / m_stats.losing_trades : 0.0;
    }
    
    bool IsPerformanceDegrading() const
    {
        if(m_stats.total_trades < 20) return false;
        
        double recent_win_rate = GetRecentWinRate();
        double overall_win_rate = GetWinRate();
        
        return (recent_win_rate < 40.0 || recent_win_rate < overall_win_rate * 0.7);
    }
    
    bool ShouldPauseTrading() const
    {
        // Check daily limits
        if(m_stats.daily_pnl <= -DailyLossLimit) return true;
        if(m_stats.daily_pnl >= DailyProfitLimit) return true;
        
        // Check consecutive losses
        if(m_consecutive_losses >= MaxConsecutiveLosses) return true;
        
        // Check severe performance degradation
        if(IsPerformanceDegrading() && m_consecutive_losses >= 3) return true;
        
        return false;
    }
    
    int GetConsecutiveLosses() const { return m_consecutive_losses; }
    int GetConsecutiveWins() const { return m_consecutive_wins; }
    double GetDailyPnL() const { return m_stats.daily_pnl; }
    
    void PrintDailyStats() const
    {
        Print("=== DAILY PERFORMANCE ===");
        Print("Total Trades: ", m_stats.total_trades);
        Print("Wins: ", m_stats.winning_trades, " | Losses: ", m_stats.losing_trades);
        Print("Win Rate: ", DoubleToString(GetWinRate(), 1), "%");
        Print("Recent Win Rate (20): ", DoubleToString(GetRecentWinRate(), 1), "%");
        Print("Daily P&L: $", DoubleToString(m_stats.daily_pnl, 2));
        Print("Total P&L: $", DoubleToString(m_stats.total_profit - m_stats.total_loss, 2));
        Print("Profit Factor: ", DoubleToString(GetProfitFactor(), 2));
        Print("Avg Win: $", DoubleToString(GetAverageWin(), 2));
        Print("Avg Loss: $", DoubleToString(GetAverageLoss(), 2));
        Print("Max Drawdown: $", DoubleToString(m_stats.max_drawdown, 2));
        Print("Current Drawdown: $", DoubleToString(m_stats.current_drawdown, 2));
        Print("Consecutive Losses: ", m_consecutive_losses);
        Print("Consecutive Wins: ", m_consecutive_wins);
    }
    
    void PrintPerformanceAlert() const
    {
        if(IsPerformanceDegrading())
        {
            Print("⚠️ PERFORMANCE ALERT: Recent performance below expectations");
            Print("Recent Win Rate: ", DoubleToString(GetRecentWinRate(), 1), "%");
            Print("Overall Win Rate: ", DoubleToString(GetWinRate(), 1), "%");
            Print("Consider parameter adjustment or market condition analysis");
        }
    }
};

//+------------------------------------------------------------------+
//| Enhanced Position Sizer Class                                   |
//+------------------------------------------------------------------+
class PositionSizer
{
private:
    double m_base_size;
    double m_max_multiplier;
    PerformanceMonitor* m_performance;
    
public:
    PositionSizer(double base_size, double max_multiplier, PerformanceMonitor* perf_monitor)
    {
        m_base_size = base_size;
        m_max_multiplier = max_multiplier;
        m_performance = perf_monitor;
    }
    
    double CalculateOptimalSize()
    {
        if(!EnablePositionSizing) return m_base_size;
        
        double optimal_size = m_base_size;
        
        // Adjust based on spread conditions
        if(g_spread_analyzer.GetSampleCount() > 20)
        {
            double avg_spread = g_spread_analyzer.GetAverageSpread();
            double current_spread = g_market_data.spread;
            
            if(avg_spread > 0)
            {
                double spread_factor = avg_spread / MathMax(current_spread, 0.1);
                spread_factor = MathMax(0.5, MathMin(1.5, spread_factor)); // Limit adjustment
                optimal_size *= spread_factor;
            }
        }
        
        // Adjust based on recent performance
        if(m_performance != NULL)
        {
            double recent_win_rate = m_performance.GetRecentWinRate();
            int consecutive_losses = m_performance.GetConsecutiveLosses();
            int consecutive_wins = m_performance.GetConsecutiveWins();
            
            // Reduce size after consecutive losses
            if(consecutive_losses >= 2)
            {
                double loss_penalty = 1.0 - (consecutive_losses * 0.15);
                loss_penalty = MathMax(0.3, loss_penalty); // Don't go below 30%
                optimal_size *= loss_penalty;
                Print("Position size reduced due to ", consecutive_losses, " consecutive losses");
            }
            
            // Increase size after consecutive wins (but carefully)
            if(consecutive_wins >= 3 && recent_win_rate > 70.0)
            {
                double win_bonus = 1.0 + (MathMin(consecutive_wins, 5) * 0.1);
                win_bonus = MathMin(m_max_multiplier, win_bonus);
                optimal_size *= win_bonus;
                Print("Position size increased due to ", consecutive_wins, " consecutive wins");
            }
            
            // Adjust based on win rate
            if(recent_win_rate < 40.0)
            {
                optimal_size *= 0.6; // Significant reduction for poor performance
                Print("Position size reduced due to low win rate: ", DoubleToString(recent_win_rate, 1), "%");
            }
            else if(recent_win_rate > 75.0)
            {
                optimal_size *= 1.3; // Moderate increase for excellent performance
                Print("Position size increased due to high win rate: ", DoubleToString(recent_win_rate, 1), "%");
            }
        }
        
        // Ensure within broker limits and our max multiplier
        double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
        double max_allowed = m_base_size * m_max_multiplier;
        
        optimal_size = MathMax(min_lot, MathMin(MathMin(max_lot, max_allowed), optimal_size));
        
        // Round to valid lot step
        double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        if(lot_step > 0)
        {
            optimal_size = MathRound(optimal_size / lot_step) * lot_step;
        }
        
        return optimal_size;
    }
    
    void PrintSizingInfo(double calculated_size) const
    {
        if(calculated_size != m_base_size)
        {
            double multiplier = calculated_size / m_base_size;
            Print("Dynamic Position Sizing: Base=", DoubleToString(m_base_size, 2),
                  " | Calculated=", DoubleToString(calculated_size, 2),
                  " | Multiplier=", DoubleToString(multiplier, 2), "x");
        }
    }
};

//+------------------------------------------------------------------+
//| Enhanced Latency Optimizer Class                                |
//+------------------------------------------------------------------+
class LatencyOptimizer
{
private:
    uint m_order_latencies[50];
    int m_latency_index;
    int m_sample_count;
    double m_avg_latency;
    double m_max_acceptable_latency;
    bool m_high_latency_mode;
    uint m_last_optimization_time;
    
public:
    LatencyOptimizer(double max_latency = 100.0)
    {
        ArrayInitialize(m_order_latencies, 0);
        m_latency_index = 0;
        m_sample_count = 0;
        m_avg_latency = 0;
        m_max_acceptable_latency = max_latency;
        m_high_latency_mode = false;
        m_last_optimization_time = 0;
    }
    
    void RecordLatency(uint latency_ms)
    {
        if(!EnableLatencyOptimization) return;
        
        m_order_latencies[m_latency_index] = latency_ms;
        m_latency_index = (m_latency_index + 1) % 50;
        if(m_sample_count < 50) m_sample_count++;
        
        // Calculate rolling average
        uint sum = 0;
        for(int i = 0; i < m_sample_count; i++)
        {
            sum += m_order_latencies[i];
        }
        m_avg_latency = (double)sum / m_sample_count;
        
        // Check if optimization is needed
        if(GetTickCount() - m_last_optimization_time > 30000) // Every 30 seconds
        {
            OptimizeExecution();
            m_last_optimization_time = GetTickCount();
        }
    }
    
    bool IsLatencyAcceptable() const
    {
        return m_avg_latency <= m_max_acceptable_latency;
    }
    
    double GetAverageLatency() const { return m_avg_latency; }
    
    bool IsHighLatencyMode() const { return m_high_latency_mode; }
    
    void OptimizeExecution()
    {
        if(m_sample_count < 10) return; // Need minimum samples
        
        bool was_high_latency = m_high_latency_mode;
        
        if(m_avg_latency > m_max_acceptable_latency * 1.5) // 50% above threshold
        {
            if(!m_high_latency_mode)
            {
                m_high_latency_mode = true;
                trade.SetTypeFilling(ORDER_FILLING_RETURN); // Market execution
                trade.SetDeviationInPoints(MaxSlippagePoints * 2); // Allow more slippage
                Print("🔄 HIGH LATENCY MODE ACTIVATED - Avg: ", DoubleToString(m_avg_latency, 1), "ms");
                Print("Switched to market execution with increased slippage tolerance");
            }
        }
        else if(m_avg_latency < m_max_acceptable_latency * 0.8) // 20% below threshold
        {
            if(m_high_latency_mode)
            {
                m_high_latency_mode = false;
                trade.SetTypeFilling(ORDER_FILLING_IOC); // Back to IOC
                trade.SetDeviationInPoints(MaxSlippagePoints); // Normal slippage
                Print("✅ NORMAL LATENCY MODE RESTORED - Avg: ", DoubleToString(m_avg_latency, 1), "ms");
                Print("Switched back to IOC execution with normal slippage");
            }
        }
        
        // Additional optimizations based on latency patterns
        if(m_sample_count >= 20)
        {
            uint min_latency = UINT_MAX;
            uint max_latency = 0;
            
            for(int i = 0; i < m_sample_count; i++)
            {
                min_latency = MathMin(min_latency, m_order_latencies[i]);
                max_latency = MathMax(max_latency, m_order_latencies[i]);
            }
            
            // High variance indicates unstable connection
            if(max_latency > min_latency * 3)
            {
                Print("⚠️ UNSTABLE CONNECTION DETECTED - Latency range: ", 
                      min_latency, "-", max_latency, "ms");
                // Could implement connection switching logic here
            }
        }
    }
    
    void PrintLatencyStats() const
    {
        if(m_sample_count < 5) return;
        
        Print("=== LATENCY STATISTICS ===");
        Print("Average Latency: ", DoubleToString(m_avg_latency, 1), "ms");
        Print("Sample Count: ", m_sample_count);
        Print("Acceptable Threshold: ", DoubleToString(m_max_acceptable_latency, 1), "ms");
        Print("High Latency Mode: ", m_high_latency_mode ? "ACTIVE" : "INACTIVE");
        Print("Status: ", IsLatencyAcceptable() ? "✅ GOOD" : "⚠️ HIGH");
    }
};

//+------------------------------------------------------------------+
//| Enhanced Market Microstructure Analysis Class                   |
//+------------------------------------------------------------------+
class MarketMicrostructure
{
private:
    double m_spread_stability_score;
    bool m_is_news_time;
    bool m_is_market_opening;
    datetime m_last_analysis_time;
    int m_tick_count_1min;
    datetime m_last_tick_reset;
    
    // News times (EST/EDT) in minutes from midnight
    int m_news_times[10];
    int m_news_count;
    
public:
    MarketMicrostructure()
    {
        m_spread_stability_score = 0;
        m_is_news_time = false;
        m_is_market_opening = false;
        m_last_analysis_time = 0;
        m_tick_count_1min = 0;
        m_last_tick_reset = TimeCurrent();
        
        // Initialize common news times (EST/EDT minutes from midnight)
        m_news_times[0] = 8 * 60 + 30;   // 8:30 AM - Employment data
        m_news_times[1] = 10 * 60;       // 10:00 AM - Various reports
        m_news_times[2] = 14 * 60;       // 2:00 PM - Fed announcements
        m_news_times[3] = 14 * 60 + 15;  // 2:15 PM - Fed press conference
        m_news_times[4] = 16 * 60;       // 4:00 PM - Market close
        m_news_count = 5;
    }
    
    void UpdateAnalysis()
    {
        // Update tick count
        datetime current_time = TimeCurrent();
        if(current_time - m_last_tick_reset >= 60) // Reset every minute
        {
            m_tick_count_1min = 0;
            m_last_tick_reset = current_time;
        }
        m_tick_count_1min++;
        
        // Check if analysis is needed (every 30 seconds)
        if(current_time - m_last_analysis_time < 30) return;
        
        AnalyzeSpreadStability();
        CheckNewsTime();
        CheckMarketOpening();
        
        m_last_analysis_time = current_time;
    }
    
    bool IsMarketConditionFavorable()
    {
        UpdateAnalysis();
        
        // Check spread stability
        if(g_spread_analyzer.GetSampleCount() > 20)
        {
            double spread_cv = g_spread_analyzer.GetStandardDeviation() / 
                              MathMax(g_spread_analyzer.GetAverageSpread(), 0.1);
            
            if(spread_cv > 0.4) // High coefficient of variation = unstable spreads
            {
                Print("❌ Unfavorable: Spread instability detected (CV: ", DoubleToString(spread_cv, 3), ")");
                return false;
            }
        }
        
        // Avoid news times
        if(m_is_news_time)
        {
            Print("❌ Unfavorable: News time detected - avoiding trades");
            return false;
        }
        
        // Avoid market opening chaos
        if(m_is_market_opening)
        {
            Print("❌ Unfavorable: Market opening period - avoiding trades");
            return false;
        }
        
        // Check tick frequency (sign of liquidity)
        if(m_tick_count_1min < 10) // Very low tick count
        {
            Print("❌ Unfavorable: Low liquidity detected (", m_tick_count_1min, " ticks/min)");
            return false;
        }
        
        // Check for recent spread spikes
        if(g_spread_analyzer.IsRecentSpike(60)) // Within last minute
        {
            Print("❌ Unfavorable: Recent spread spike detected");
            return false;
        }
        
        return true;
    }
    
private:
    void AnalyzeSpreadStability()
    {
        if(g_spread_analyzer.GetSampleCount() < 10) return;
        
        double avg_spread = g_spread_analyzer.GetAverageSpread();
        double std_dev = g_spread_analyzer.GetStandardDeviation();
        double current_spread = g_market_data.spread;
        
        // Calculate stability score (0-1, higher is better)
        if(avg_spread > 0)
        {
            double cv = std_dev / avg_spread; // Coefficient of variation
            m_spread_stability_score = 1.0 / (1.0 + cv * 10.0); // Sigmoid-like transformation
        }
    }
    
    void CheckNewsTime()
    {
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        
        // Convert to EST/EDT (assuming broker time adjustment is handled elsewhere)
        int current_minutes = dt.hour * 60 + dt.min;
        
        m_is_news_time = false;
        for(int i = 0; i < m_news_count; i++)
        {
            if(MathAbs(current_minutes - m_news_times[i]) <= 15) // 15-minute buffer
            {
                m_is_news_time = true;
                break;
            }
        }
    }
    
    void CheckMarketOpening()
    {
        MqlDateTime dt;
        TimeToStruct(TimeCurrent(), dt);
        
        // Check if within first 30 minutes of market open (9:30 AM EST/EDT)
        int market_open_minutes = 9 * 60 + 30; // 9:30 AM
        int current_minutes = dt.hour * 60 + dt.min;
        
        // Consider market opening period
        m_is_market_opening = (dt.day_of_week >= 1 && dt.day_of_week <= 5) && // Weekday
                              (current_minutes >= market_open_minutes && 
                               current_minutes <= market_open_minutes + 30); // First 30 minutes
    }
    
public:
    double GetSpreadStabilityScore() const { return m_spread_stability_score; }
    bool IsNewsTime() const { return m_is_news_time; }
    bool IsMarketOpening() const { return m_is_market_opening; }
    int GetTickCount() const { return m_tick_count_1min; }
    
    void PrintMicrostructureInfo() const
    {
        Print("=== MARKET MICROSTRUCTURE ===");
        Print("Spread Stability Score: ", DoubleToString(m_spread_stability_score, 3));
        Print("News Time: ", m_is_news_time ? "YES ⚠️" : "NO ✅");
        Print("Market Opening: ", m_is_market_opening ? "YES ⚠️" : "NO ✅");
        Print("Tick Count (1min): ", m_tick_count_1min);
        Print("Overall Condition: ", IsMarketConditionFavorable() ? "FAVORABLE ✅" : "UNFAVORABLE ❌");
    }
};

//+------------------------------------------------------------------+
//| Enhanced Error Handler                                           |
//+------------------------------------------------------------------+
void HandleTradingError(uint error_code, string context, int &error_count)
{
    static datetime last_error_time = 0;
    static int consecutive_errors = 0;
    
    datetime current_time = TimeCurrent();
    
    // Track consecutive errors
    if(current_time - last_error_time < 60) // Within 1 minute
    {
        consecutive_errors++;
    }
    else
    {
        consecutive_errors = 1;
    }
    
    last_error_time = current_time;
    error_count++;
    
    Print("🚨 TRADING ERROR [", context, "]: ", trade.ResultRetcodeDescription(), 
          " (", error_code, ") | Consecutive: ", consecutive_errors);
    
    switch(error_code)
    {
        case TRADE_RETCODE_MARKET_CLOSED:
            Print("📊 Market closed - entering hibernation mode");
            EnableTrading = false; // Will be re-enabled by market hours check
            break;
            
        case TRADE_RETCODE_NO_MONEY:
            Print("💰 CRITICAL: Insufficient funds - stopping EA");
            Print("Account Balance: $", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
            Print("Account Equity: $", DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2));
            EmergencyCloseAll();
            EnableTrading = false;
            break;
            
        case TRADE_RETCODE_CONNECTION:
            Print("🌐 Connection issues - will retry after delay");
            if(consecutive_errors >= 3)
            {
                Print("Multiple connection errors - increasing wait time");
                Sleep(60000); // 1 minute wait
            }
            else
            {
                Sleep(10000); // 10 second wait
            }
            break;
            
        case TRADE_RETCODE_TRADE_DISABLED:
            Print("🚫 Trading disabled by broker - checking account status");
            if(!AccountInfoInteger(ACCOUNT_TRADE_ALLOWED))
            {
                Print("⚠️ ALERT: Trading not allowed on account");
                EnableTrading = false;
            }
            break;
            
        case TRADE_RETCODE_INVALID_PRICE:
        case TRADE_RETCODE_PRICE_CHANGED:
        case TRADE_RETCODE_PRICE_OFF:
            Print("💲 Price-related error - market may be volatile");
            if(consecutive_errors >= 5)
            {
                Print("Multiple price errors - suspending trading for 5 minutes");
                Sleep(300000);
            }
            break;
            
        case TRADE_RETCODE_SERVER_DISABLES_AT:
            Print("🔧 Server maintenance mode detected");
            Sleep(60000); // Wait 1 minute
            break;
            
        case TRADE_RETCODE_REQUOTE:
            Print("📈 Requote received - market moving fast");
            // Requotes are normal in fast markets, just retry
            break;
            
        case TRADE_RETCODE_TOO_MANY_REQUESTS:
            Print("⚡ Too many requests - throttling detected");
            Sleep(5000); // 5 second cool-down
            break;
            
        default:
            Print("❓ Unknown error code: ", error_code);
            if(consecutive_errors >= 10)
            {
                Print("🛑 CRITICAL: Too many consecutive errors - emergency shutdown");
                EmergencyCloseAll();
                EnableTrading = false;
            }
            break;
    }
    
    // Additional safety measures for excessive errors
    if(consecutive_errors >= 20)
    {
        Print("🚨 EMERGENCY: Excessive trading errors - shutting down EA");
        EmergencyCloseAll();
        EnableTrading = false;
    }
    
    // Log error statistics
    if(error_count % 10 == 0)
    {
        Print("📊 Error Statistics: Total errors in session: ", error_count);
        Print("Recent consecutive errors: ", consecutive_errors);
    }
}

//+------------------------------------------------------------------+
//| Enhanced Rolling Spread Analyzer Class (Memory Optimized)      |
//+------------------------------------------------------------------+
class SpreadAnalyzer
{
private:
    double m_spreads[200];          // Rolling window of spreads
    int m_index;                    // Current index in circular buffer
    int m_count;                    // Number of samples collected
    double m_sum;                   // Sum for efficient average calculation
    double m_sum_squares;           // Sum of squares for variance
    double m_min_spread;            // Minimum spread in window
    double m_max_spread;            // Maximum spread in window
    double m_avg_spread;            // Current average spread
    double m_std_deviation;         // Standard deviation
    datetime m_last_update;         // Last update time
    
    // Spike detection
    int m_spike_count;              // Number of spikes detected
    datetime m_last_spike_time;     // Time of last spike
    
    // Memory optimization
    int m_optimization_counter;     // Counter for memory optimization
    
public:
    SpreadAnalyzer() { Reset(); }
    
    void Reset()
    {
        m_index = 0;
        m_count = 0;
        m_sum = 0;
        m_sum_squares = 0;
        m_min_spread = 999.0;
        m_max_spread = 0.0;
        m_avg_spread = 0.0;
        m_std_deviation = 0.0;
        m_spike_count = 0;
        m_last_spike_time = 0;
        m_last_update = 0;
        m_optimization_counter = 0;
        
        // Initialize array
        ArrayInitialize(m_spreads, 0.0);
    }
    
    void AddSpread(double spread)
    {
        if(spread <= 0) return; // Invalid spread
        
        // Memory optimization every 150 samples
        m_optimization_counter++;
        if(m_optimization_counter >= 150)
        {
            OptimizeMemory();
            m_optimization_counter = 0;
        }
        
        bool is_new_sample = true;
        
        // If we have a full buffer, subtract the old value
        if(m_count >= 200)
        {
            double old_spread = m_spreads[m_index];
            m_sum -= old_spread;
            m_sum_squares -= (old_spread * old_spread);
            is_new_sample = false;
        }
        
        // Add new spread
        m_spreads[m_index] = spread;
        m_sum += spread;
        m_sum_squares += (spread * spread);
        
        // Update index and count
        m_index = (m_index + 1) % 200;
        if(is_new_sample && m_count < 200) m_count++;
        
        // Recalculate statistics
        RecalculateStats();
        
        // Check for spike
        CheckForSpike(spread);
        
        m_last_update = TimeCurrent();
    }
    
    // ENHANCEMENT 1: Memory Optimization
    void OptimizeMemory()
    {
        if(m_count < 100) return; // Not enough data to optimize
        
        Print("🔧 MEMORY OPTIMIZATION: Compressing spread data");
        
        // Keep every 2nd sample from first half, all samples from second half
        int new_count = 0;
        double new_sum = 0;
        double new_sum_squares = 0;
        
        // Compress first half (keep every 2nd sample)
        for(int i = 0; i < m_count / 2; i += 2)
        {
            if(m_spreads[i] > 0)
            {
                m_spreads[new_count] = m_spreads[i];
                new_sum += m_spreads[i];
                new_sum_squares += (m_spreads[i] * m_spreads[i]);
                new_count++;
            }
        }
        
        // Keep all samples from second half
        for(int i = m_count / 2; i < m_count; i++)
        {
            if(m_spreads[i] > 0)
            {
                m_spreads[new_count] = m_spreads[i];
                new_sum += m_spreads[i];
                new_sum_squares += (m_spreads[i] * m_spreads[i]);
                new_count++;
            }
        }
        
        // Clear remaining array elements
        for(int i = new_count; i < 200; i++)
        {
            m_spreads[i] = 0.0;
        }
        
        // Update counters
        m_count = new_count;
        m_index = new_count % 200;
        m_sum = new_sum;
        m_sum_squares = new_sum_squares;
        
        // Recalculate statistics
        RecalculateStats();
        
        Print("✅ Memory optimization complete: ", new_count, " samples retained");
    }
    
private:
    void RecalculateStats()
    {
        if(m_count == 0) return;
        
        // Calculate average
        m_avg_spread = m_sum / m_count;
        
        // Calculate min/max by scanning the active window
        m_min_spread = 999.0;
        m_max_spread = 0.0;
        
        for(int i = 0; i < m_count; i++)
        {
            double spread = m_spreads[i];
            if(spread > 0) // Valid spread
            {
                m_min_spread = MathMin(m_min_spread, spread);
                m_max_spread = MathMax(m_max_spread, spread);
            }
        }
        
        // Calculate standard deviation
        if(m_count > 1)
        {
            double variance = (m_sum_squares / m_count) - (m_avg_spread * m_avg_spread);
            variance = MathMax(0.0, variance); // Ensure non-negative
            m_std_deviation = MathSqrt(variance);
        }
        else
        {
            m_std_deviation = 0.0;
        }
    }
    
    void CheckForSpike(double current_spread)
    {
        if(m_count < 20) return; // Need minimum samples
        
        // Spike detection: spread > average + (threshold * std_dev)
        double spike_threshold = m_avg_spread + (SpreadSpikeThreshold * m_std_deviation);
        
        if(current_spread > spike_threshold)
        {
            m_spike_count++;
            m_last_spike_time = TimeCurrent();
            
            Print("📊 SPREAD SPIKE DETECTED: Current=", DoubleToString(current_spread, 2),
                  " | Average=", DoubleToString(m_avg_spread, 2),
                  " | Threshold=", DoubleToString(spike_threshold, 2),
                  " | StdDev=", DoubleToString(m_std_deviation, 2));
        }
    }
    
public:
    // Public accessors
    double GetAverageSpread() const { return m_avg_spread; }
    double GetMinSpread() const { return m_min_spread; }
    double GetMaxSpread() const { return m_max_spread; }
    double GetStandardDeviation() const { return m_std_deviation; }
    int GetSampleCount() const { return m_count; }
    int GetSpikeCount() const { return m_spike_count; }
    datetime GetLastSpikeTime() const { return m_last_spike_time; }
    
    // Analysis methods
    bool IsSpreadNormal(double current_spread) const
    {
        if(m_count < 20) return true; // Not enough data
        
        // Consider normal if within 2 standard deviations
        double upper_bound = m_avg_spread + (2.0 * m_std_deviation);
        double lower_bound = MathMax(0.1, m_avg_spread - (2.0 * m_std_deviation));
        
        return (current_spread >= lower_bound && current_spread <= upper_bound);
    }
    
    bool IsSpreadSpike(double current_spread) const
    {
        if(m_count < 20) return false;
        
        double spike_threshold = m_avg_spread + (SpreadSpikeThreshold * m_std_deviation);
        return (current_spread > spike_threshold);
    }
    
    bool IsRecentSpike(int seconds_threshold = 30) const
    {
        return (m_last_spike_time > 0 && (TimeCurrent() - m_last_spike_time) < seconds_threshold);
    }
    
    double GetSpreadPercentile(double percentile) const
    {
        if(m_count < 10) return m_avg_spread;
        
        // Simple percentile calculation
        double sorted_spreads[200];
        for(int i = 0; i < m_count; i++)
        {
            sorted_spreads[i] = m_spreads[i];
        }
        
        // Bubble sort (simple for small arrays)
        for(int i = 0; i < m_count - 1; i++)
        {
            for(int j = 0; j < m_count - i - 1; j++)
            {
                if(sorted_spreads[j] > sorted_spreads[j + 1])
                {
                    double temp = sorted_spreads[j];
                    sorted_spreads[j] = sorted_spreads[j + 1];
                    sorted_spreads[j + 1] = temp;
                }
            }
        }
        
        int index = (int)((percentile / 100.0) * (m_count - 1));
        index = MathMax(0, MathMin(index, m_count - 1));
        
        return sorted_spreads[index];
    }
    
    void PrintStatistics() const
    {
        if(m_count < 10) return;
        
        Print("=== SPREAD STATISTICS ===");
        Print("Samples: ", m_count);
        Print("Average: ", DoubleToString(m_avg_spread, 2), " pips");
        Print("Min: ", DoubleToString(m_min_spread, 2), " pips");
        Print("Max: ", DoubleToString(m_max_spread, 2), " pips");
        Print("Std Dev: ", DoubleToString(m_std_deviation, 2), " pips");
        Print("95th Percentile: ", DoubleToString(GetSpreadPercentile(95), 2), " pips");
        Print("Spikes Detected: ", m_spike_count);
        Print("Last Spike: ", TimeToString(m_last_spike_time));
        Print("Memory Usage: ", m_count, "/200 samples");
    }
};

//+------------------------------------------------------------------+
//| Enhanced Order Execution Class (with Latency Tracking)         |
//+------------------------------------------------------------------+
class OrderExecutor
{
private:
    CTrade* m_trade;
    int m_magic_number;
    int m_max_retries;
    int m_retry_delay_ms;
    LatencyOptimizer* m_latency_optimizer;
    
    // Execution statistics
    int m_total_attempts;
    int m_successful_executions;
    int m_failed_executions;
    double m_avg_execution_time;
    
public:
    OrderExecutor(CTrade* trade_obj, int magic, LatencyOptimizer* latency_opt, int retries = 3, int delay_ms = 50)
    {
        m_trade = trade_obj;
        m_magic_number = magic;
        m_latency_optimizer = latency_opt;
        m_max_retries = retries;
        m_retry_delay_ms = delay_ms;
        
        m_total_attempts = 0;
        m_successful_executions = 0;
        m_failed_executions = 0;
        m_avg_execution_time = 0;
    }
    
    bool PlaceOptimizedOrder(ENUM_ORDER_TYPE order_type, double volume, double price, 
                           double sl = 0, double tp = 0, datetime expiration = 0)
    {
        uint start_time = GetTickCount();
        bool result = false;
        string order_name = EnumToString(order_type);
        static int error_count = 0;
        
        // Validate input parameters
        if(!ValidateOrderParameters(order_type, volume, price))
        {
            Print("ERROR: Invalid order parameters for ", order_name);
            return false;
        }
        
        // Adjust execution strategy based on latency conditions
        ENUM_ORDER_TYPE_FILLING filling_modes[3];
        int mode_count = 3;
        
        if(m_latency_optimizer != NULL && m_latency_optimizer.IsHighLatencyMode())
        {
            // High latency: prefer market execution
            filling_modes[0] = ORDER_FILLING_RETURN;
            filling_modes[1] = ORDER_FILLING_IOC;
            filling_modes[2] = ORDER_FILLING_FOK;
        }
        else
        {
            // Normal latency: prefer precise execution
            filling_modes[0] = ORDER_FILLING_FOK;
            filling_modes[1] = ORDER_FILLING_IOC;
            filling_modes[2] = ORDER_FILLING_RETURN;
        }
        
        double adjusted_price = price;
        double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
        
        for(int mode_idx = 0; mode_idx < mode_count && !result; mode_idx++)
        {
            m_trade.SetTypeFilling(filling_modes[mode_idx]);
            
            for(int attempt = 0; attempt < m_max_retries && !result; attempt++)
            {
                m_total_attempts++;
                
                // Log attempt
                Print("🎯 Order attempt ", attempt + 1, "/", m_max_retries, 
                      " | Type: ", order_name,
                      " | Price: ", DoubleToString(adjusted_price, 5),
                      " | Mode: ", EnumToString(filling_modes[mode_idx]));
                
                uint attempt_start = GetTickCount();
                
                // Place order based on type
                result = ExecuteOrder(order_type, volume, adjusted_price, sl, tp, expiration);
                
                uint attempt_time = GetTickCount() - attempt_start;
                
                if(result)
                {
                    m_successful_executions++;
                    uint total_execution_time = GetTickCount() - start_time;
                    UpdateExecutionStats(total_execution_time);
                    
                    // Record latency
                    if(m_latency_optimizer != NULL)
                    {
                        m_latency_optimizer.RecordLatency(total_execution_time);
                    }
                    
                    Print("✅ ORDER SUCCESS: ", order_name, " | Ticket: ", m_trade.ResultOrder(),
                          " | Price: ", DoubleToString(adjusted_price, 5),
                          " | Time: ", total_execution_time, "ms",
                          " | Mode: ", EnumToString(filling_modes[mode_idx]));
                    
                    return true;
                }
                
                // Handle failure
                uint error_code = m_trade.ResultRetcode();
                string error_desc = m_trade.ResultRetcodeDescription();
                
                Print("❌ Order attempt failed: ", error_desc, " (", error_code, ") | Time: ", attempt_time, "ms");
                
                // Enhanced error handling
                HandleTradingError(error_code, "Order Execution", error_count);
                
                // Adjust price for next attempt if price-related error
                if(IsPriceRelatedError(error_code))
                {
                    adjusted_price = AdjustPriceForRetry(order_type, adjusted_price, tick_size, attempt + 1);
                }
                
                // Wait before retry (except last attempt)
                if(attempt < m_max_retries - 1)
                {
                    Sleep(m_retry_delay_ms);
                }
            }
        }
        
        // All attempts failed
        m_failed_executions++;
        uint total_time = GetTickCount() - start_time;
        
        // Record failed attempt latency too
        if(m_latency_optimizer != NULL)
        {
            m_latency_optimizer.RecordLatency(total_time);
        }
        
        Print("🚫 ORDER FAILED: ", order_name, " after ", m_max_retries * mode_count, " attempts | Total time: ", total_time, "ms");
        return false;
    }
    
private:
    bool ValidateOrderParameters(ENUM_ORDER_TYPE order_type, double volume, double price)
    {
        // Volume validation
        double min_volume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
        double max_volume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
        double volume_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
        
        if(volume < min_volume || volume > max_volume)
        {
            Print("Invalid volume: ", volume, " (Min: ", min_volume, ", Max: ", max_volume, ")");
            return false;
        }
        
        // Price validation
        if(price <= 0)
        {
            Print("Invalid price: ", price);
            return false;
        }
        
        // Market-specific validation
        double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        
        if(bid <= 0 || ask <= 0)
        {
            Print("Invalid market prices - Bid: ", bid, ", Ask: ", ask);
            return false;
        }
        
        return true;
    }
    
    bool ExecuteOrder(ENUM_ORDER_TYPE order_type, double volume, double price, 
                     double sl, double tp, datetime expiration)
    {
        datetime exp_time = (expiration == 0) ? TimeCurrent() + 3600 : expiration;
        
        switch(order_type)
        {
            case ORDER_TYPE_BUY_STOP:
                return m_trade.BuyStop(volume, price, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, exp_time);
                
            case ORDER_TYPE_SELL_STOP:
                return m_trade.SellStop(volume, price, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, exp_time);
                
            case ORDER_TYPE_BUY_LIMIT:
                return m_trade.BuyLimit(volume, price, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, exp_time);
                
            case ORDER_TYPE_SELL_LIMIT:
                return m_trade.SellLimit(volume, price, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, exp_time);
                
            case ORDER_TYPE_BUY:
                return m_trade.Buy(volume, _Symbol, price, sl, tp);
                
            case ORDER_TYPE_SELL:
                return m_trade.Sell(volume, _Symbol, price, sl, tp);
                
            default:
                Print("Unsupported order type: ", EnumToString(order_type));
                return false;
        }
    }
    
    bool IsPriceRelatedError(uint error_code)
    {
        switch(error_code)
        {
            case TRADE_RETCODE_INVALID_PRICE:
            case TRADE_RETCODE_INVALID_STOPS:
            case TRADE_RETCODE_INVALID_PRICE_IN_THE_ORDER:
            case TRADE_RETCODE_PRICE_CHANGED:
            case TRADE_RETCODE_PRICE_OFF:
            case TRADE_RETCODE_INVALID_EXPIRATION:
                return true;
            default:
                return false;
        }
    }
    
    double AdjustPriceForRetry(ENUM_ORDER_TYPE order_type, double original_price, 
                              double tick_size, int attempt_number)
    {
        double adjustment = tick_size * attempt_number;
        
        // Adjust price to improve execution probability
        switch(order_type)
        {
            case ORDER_TYPE_BUY_STOP:
            case ORDER_TYPE_BUY_LIMIT:
                return original_price + adjustment; // Move up for buy orders
                
            case ORDER_TYPE_SELL_STOP:
            case ORDER_TYPE_SELL_LIMIT:
                return original_price - adjustment; // Move down for sell orders
                
            default:
                return original_price;
        }
    }
    
    void UpdateExecutionStats(uint execution_time)
    {
        // Update rolling average execution time
        if(m_successful_executions == 1)
        {
            m_avg_execution_time = execution_time;
        }
        else
        {
            m_avg_execution_time = (m_avg_execution_time * 0.9) + (execution_time * 0.1);
        }
    }
    
public:
    // Statistics accessors
    double GetSuccessRate() const
    {
        return (m_total_attempts > 0) ? (double)m_successful_executions / m_total_attempts * 100.0 : 0.0;
    }
    
    double GetAverageExecutionTime() const { return m_avg_execution_time; }
    int GetTotalAttempts() const { return m_total_attempts; }
    int GetSuccessfulExecutions() const { return m_successful_executions; }
    int GetFailedExecutions() const { return m_failed_executions; }
    
    void PrintExecutionStats() const
    {
        Print("=== EXECUTION STATISTICS ===");
        Print("Total Attempts: ", m_total_attempts);
        Print("Successful: ", m_successful_executions);
        Print("Failed: ", m_failed_executions);
        Print("Success Rate: ", DoubleToString(GetSuccessRate(), 1), "%");
        Print("Avg Execution Time: ", DoubleToString(m_avg_execution_time, 1), "ms");
    }
};

// Global instances
MarketData g_market_data;
SpreadAnalyzer g_spread_analyzer;
OrderExecutor* g_order_executor;
PerformanceMonitor* g_performance_monitor;
PositionSizer* g_position_sizer;
LatencyOptimizer* g_latency_optimizer;
MarketMicrostructure* g_market_analysis;

// Original global variables (preserved from original code)
double g_pip_value = 0.01;
double g_pip_size = 1.0;
double g_tick_size = 1.0;
double g_tick_value = 0.01;
int g_broker_utc_offset = 0;
int g_spread_cost_pips;
double g_last_spread = 0;
datetime g_last_trade_time = 0;
datetime g_spread_spike_time = 0;
bool g_position_active = false;
ulong g_buy_ticket = 0;
ulong g_sell_ticket = 0;
double g_entry_price = 0;
double g_break_even_price = 0;
double g_trailing_stop = 0;
bool g_market_closed_positions = false;
bool g_timezone_validated = false;

//+------------------------------------------------------------------+
//| Market Data Update Function                                     |
//+------------------------------------------------------------------+
bool UpdateMarketData()
{
    // Get fresh market data
    g_market_data.bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    g_market_data.ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    g_market_data.quote_time = (datetime)SymbolInfoInteger(_Symbol, SYMBOL_TIME);
    
    // Validate data
    if(g_market_data.bid <= 0 || g_market_data.ask <= 0)
    {
        g_market_data.valid = false;
        return false;
    }
    
    // Calculate derived values
    g_market_data.spread = g_market_data.ask - g_market_data.bid;
    g_market_data.mid_price = (g_market_data.bid + g_market_data.ask) / 2.0;
    
    // Try to get volume data (may not be available on all brokers)
    g_market_data.bid_volume = SymbolInfoInteger(_Symbol, SYMBOL_VOLUME_HIGH);
    g_market_data.ask_volume = SymbolInfoInteger(_Symbol, SYMBOL_VOLUME_LOW);
    
    g_market_data.valid = (g_market_data.spread > 0);
    g_market_data.updated = true;
    
    // Update spread analyzer
    if(g_market_data.valid)
    {
        g_spread_analyzer.AddSpread(g_market_data.spread);
    }
    
    return g_market_data.valid;
}