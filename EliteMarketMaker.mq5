//+------------------------------------------------------------------+
//| EliteMarketMaker.mq5                                            |
//| Advanced Market Making Algorithm                                |
//|                                                                  |
//| This Expert Advisor implements a sophisticated market making    |
//| strategy that provides liquidity to the market while managing    |
//| risk through advanced algorithms and dynamic position sizing.  |
//+------------------------------------------------------------------+
#property copyright "Elite Trading Systems"
#property version "2.0"
#property strict

#include <Trade\Trade.mqh>
#include <Indicators\Indicators.mqh>

//--- Input parameters for the Market Maker EA ---
input double BaseLotSize = 0.01; // Base lot size for market making
input int MagicNumber = 888888; // Unique identifier for orders
input int MaxPositions = 10; // Maximum number of simultaneous positions
input double MaxDrawdownPercent = 2.0; // Maximum drawdown percentage before reducing positions
input double RiskPerTradePercent = 0.5; // Risk percentage per trade
input int SpreadThreshold = 10; // Minimum spread in points to place orders
input int OrderDistancePoints = 20; // Distance of orders from market price in points
input int TakeProfitPoints = 50; // Take profit distance in points
input int MaxOrderLifeTimeMinutes = 30; // Maximum time to keep orders alive
input bool EnableDynamicPositionSizing = true; // Enable dynamic position sizing based on volatility
input double VolatilityMultiplier = 1.5; // Multiplier for position sizing based on ATR
input bool EnableNewsFilter = true; // Enable news-based trading filter
input int NewsImpactMinutes = 30; // Minutes to avoid trading after major news

//--- Global variables for indicator handles ---
int handle_atr;
int handle_volume;

//--- Global trading object ---
CTrade trade;

//--- Structure to store market making orders ---
struct MarketOrder
{
    ulong ticket;
    datetime time_placed;
    double price;
    ENUM_ORDER_TYPE order_type;
    double lot_size;
    double take_profit;
};

//--- Global arrays for tracking orders ---
MarketOrder buy_orders[];
MarketOrder sell_orders[];

//--- Risk management variables ---
double account_balance = 0;
double max_equity = 0;
double current_drawdown = 0;

//--- Market state variables ---
double current_spread = 0;
double volatility = 0;
datetime last_news_time = 0;

//--- File for logging ---
string log_file = "EliteMarketMaker_Log.txt";

//--- Forward declarations ---
double CalculatePositionSize();
double GetMarketVolatility();
bool IsMarketSafeForTrading();
void LogMessage(string message);
void UpdateRiskManagement();
void CancelExpiredOrders();
void PlaceMarketMakingOrders();
bool IsOrderExpired(datetime order_time);
void ManageExistingPositions();
double CalculateOrderDistance();

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("💎 Elite Market Maker: Initialization Commencing...");
    
    // Initialize the CTrade object
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetTypeFilling(ORDER_FILLING_IOC); // Immediate or Cancel for market making
    trade.SetDeviationInPoints(10);
    
    //--- Initialize standard indicator handles ---
    handle_atr = iATR(_Symbol, _Period, 14);
    handle_volume = iVolume(_Symbol, _Period);
    
    // --- Critical Check: Validate Indicator Handles ---
    if(handle_atr == INVALID_HANDLE || handle_volume == INVALID_HANDLE)
    {
        Print("CRITICAL ERROR: Failed to initialize one or more indicators. Last Error: ", GetLastError());
        return INIT_FAILED;
    }
    Print("All core indicators initialized successfully.");
    
    // Initialize account information
    account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    max_equity = account_balance;
    
    // Create log file
    int file_handle = FileOpen(log_file, FILE_WRITE|FILE_TXT);
    if(file_handle != INVALID_HANDLE)
    {
        FileWrite(file_handle, "Elite Market Maker Log - Started: ", TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS));
        FileClose(file_handle);
    }
    
    LogMessage("Elite Market Maker initialized successfully");
    Print("💎 Elite Market Maker: Initialization Complete. Ready for Market Making!");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    LogMessage("Elite Market Maker shutting down");
    Print("Elite Market Maker deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update risk management parameters
    UpdateRiskManagement();
    
    // Check if market is safe for trading
    if(!IsMarketSafeForTrading())
    {
        CancelExpiredOrders();
        return;
    }
    
    // Cancel expired orders
    CancelExpiredOrders();
    
    // Manage existing positions
    ManageExistingPositions();
    
    // Place new market making orders
    PlaceMarketMakingOrders();
}

//+------------------------------------------------------------------+
//| Expert timer function                                            |
//+------------------------------------------------------------------+
void OnTimer()
{
    // This function can be used for periodic tasks
    // Currently handled in OnTick for more responsive market making
}

//+------------------------------------------------------------------+
//| Calculate dynamic position size based on volatility             |
//+------------------------------------------------------------------+
double CalculatePositionSize()
{
    if(!EnableDynamicPositionSizing)
        return BaseLotSize;
    
    // Get current volatility
    double current_volatility = GetMarketVolatility();
    
    // Adjust position size based on volatility
    double adjusted_lot_size = BaseLotSize * (1 / (current_volatility * VolatilityMultiplier));
    
    // Ensure lot size doesn't go below minimum
    adjusted_lot_size = MathMax(0.01, adjusted_lot_size);
    
    // Ensure lot size doesn't exceed account risk limits
    double max_risk_lot = (account_balance * (RiskPerTradePercent / 100)) / (OrderDistancePoints * SymbolInfoDouble(_Symbol, SYMBOL_POINT));
    adjusted_lot_size = MathMin(adjusted_lot_size, max_risk_lot);
    
    return adjusted_lot_size;
}

//+------------------------------------------------------------------+
//| Get market volatility using ATR                                 |
//+------------------------------------------------------------------+
double GetMarketVolatility()
{
    double atr_val[1];
    if(CopyBuffer(handle_atr, 0, 0, 1, atr_val) > 0)
    {
        return atr_val[0];
    }
    return SymbolInfoDouble(_Symbol, SYMBOL_POINT) * 10; // Default small volatility
}

//+------------------------------------------------------------------+
//| Check if market is safe for trading                             |
//+------------------------------------------------------------------+
bool IsMarketSafeForTrading()
{
    // Check drawdown
    if(current_drawdown > MaxDrawdownPercent)
    {
        LogMessage("Drawdown exceeded limit: " + DoubleToString(current_drawdown, 2) + "%");
        return false;
    }
    
    // Check spread
    current_spread = SymbolInfoDouble(_Symbol, SYMBOL_ASK) - SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double spread_points = current_spread / SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    if(spread_points > SpreadThreshold)
    {
        LogMessage("Spread too high: " + DoubleToString(spread_points, 0) + " points");
        return false;
    }
    
    // Check news filter
    if(EnableNewsFilter && (TimeCurrent() - last_news_time) < (NewsImpactMinutes * 60))
    {
        LogMessage("News filter active, avoiding trading");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Log message to file                                             |
//+------------------------------------------------------------------+
void LogMessage(string message)
{
    int file_handle = FileOpen(log_file, FILE_WRITE|FILE_READ);
    if(file_handle != INVALID_HANDLE)
    {
        FileSeek(file_handle, 0, SEEK_END);
        FileWrite(file_handle, TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS) + " - " + message);
        FileClose(file_handle);
    }
}

//+------------------------------------------------------------------+
//| Update risk management parameters                               |
//+------------------------------------------------------------------+
void UpdateRiskManagement()
{
    // Update account balance and equity
    account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    
    // Update maximum equity
    max_equity = MathMax(max_equity, current_equity);
    
    // Calculate current drawdown
    if(max_equity > 0)
        current_drawdown = ((max_equity - current_equity) / max_equity) * 100;
    else
        current_drawdown = 0;
}

//+------------------------------------------------------------------+
//| Cancel expired orders                                           |
//+------------------------------------------------------------------+
void CancelExpiredOrders()
{
    // Cancel expired buy orders
    for(int i = ArraySize(buy_orders) - 1; i >= 0; i--)
    {
        if(IsOrderExpired(buy_orders[i].time_placed))
        {
            if(OrderSelect(buy_orders[i].ticket, SELECT_BY_TICKET))
            {
                if(OrderType() == ORDER_TYPE_BUY_LIMIT)
                {
                    if(!trade.OrderDelete(buy_orders[i].ticket))
                    {
                        LogMessage("Failed to delete expired buy order: " + IntegerToString(buy_orders[i].ticket));
                    }
                    else
                    {
                        LogMessage("Deleted expired buy order: " + IntegerToString(buy_orders[i].ticket));
                    }
                }
            }
            ArrayRemove(buy_orders, i);
        }
    }
    
    // Cancel expired sell orders
    for(int i = ArraySize(sell_orders) - 1; i >= 0; i--)
    {
        if(IsOrderExpired(buy_orders[i].time_placed))
        {
            if(OrderSelect(sell_orders[i].ticket, SELECT_BY_TICKET))
            {
                if(OrderType() == ORDER_TYPE_SELL_LIMIT)
                {
                    if(!trade.OrderDelete(sell_orders[i].ticket))
                    {
                        LogMessage("Failed to delete expired sell order: " + IntegerToString(sell_orders[i].ticket));
                    }
                    else
                    {
                        LogMessage("Deleted expired sell order: " + IntegerToString(sell_orders[i].ticket));
                    }
                }
            }
            ArrayRemove(sell_orders, i);
        }
    }
}

//+------------------------------------------------------------------+
//| Place market making orders                                      |
//+------------------------------------------------------------------+
void PlaceMarketMakingOrders()
{
    // Get current market prices
    double bid_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double ask_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double midpoint = (bid_price + ask_price) / 2.0;
    
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double order_distance = CalculateOrderDistance();
    
    // Calculate lot size
    double lot_size = CalculatePositionSize();
    
    // Place buy limit order below market
    if(ArraySize(buy_orders) < MaxPositions/2)
    {
        double buy_price = midpoint - (order_distance * point);
        double take_profit = buy_price + (TakeProfitPoints * point);
        
        // Check if we already have an order at this level
        bool order_exists = false;
        for(int i = 0; i < ArraySize(buy_orders); i++)
        {
            if(MathAbs(buy_orders[i].price - buy_price) < (point * 5))
            {
                order_exists = true;
                break;
            }
        }
        
        if(!order_exists)
        {
            ulong ticket = trade.OrderSend(_Symbol, ORDER_TYPE_BUY_LIMIT, lot_size, buy_price, 0, 0, take_profit, "MM Buy Limit");
            if(ticket > 0)
            {
                MarketOrder new_order;
                new_order.ticket = ticket;
                new_order.time_placed = TimeCurrent();
                new_order.price = buy_price;
                new_order.order_type = ORDER_TYPE_BUY_LIMIT;
                new_order.lot_size = lot_size;
                new_order.take_profit = take_profit;
                
                ArrayResize(buy_orders, ArraySize(buy_orders) + 1);
                buy_orders[ArraySize(buy_orders) - 1] = new_order;
                
                LogMessage("Placed buy limit order: " + DoubleToString(buy_price, _Digits) + 
                          " Lot: " + DoubleToString(lot_size, 2) + 
                          " TP: " + DoubleToString(take_profit, _Digits));
            }
        }
    }
    
    // Place sell limit order above market
    if(ArraySize(sell_orders) < MaxPositions/2)
    {
        double sell_price = midpoint + (order_distance * point);
        double take_profit = sell_price - (TakeProfitPoints * point);
        
        // Check if we already have an order at this level
        bool order_exists = false;
        for(int i = 0; i < ArraySize(sell_orders); i++)
        {
            if(MathAbs(sell_orders[i].price - sell_price) < (point * 5))
            {
                order_exists = true;
                break;
            }
        }
        
        if(!order_exists)
        {
            ulong ticket = trade.OrderSend(_Symbol, ORDER_TYPE_SELL_LIMIT, lot_size, sell_price, 0, 0, take_profit, "MM Sell Limit");
            if(ticket > 0)
            {
                MarketOrder new_order;
                new_order.ticket = ticket;
                new_order.time_placed = TimeCurrent();
                new_order.price = sell_price;
                new_order.order_type = ORDER_TYPE_SELL_LIMIT;
                new_order.lot_size = lot_size;
                new_order.take_profit = take_profit;
                
                ArrayResize(sell_orders, ArraySize(sell_orders) + 1);
                sell_orders[ArraySize(sell_orders) - 1] = new_order;
                
                LogMessage("Placed sell limit order: " + DoubleToString(sell_price, _Digits) + 
                          " Lot: " + DoubleToString(lot_size, 2) + 
                          " TP: " + DoubleToString(take_profit, _Digits));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if order is expired                                       |
//+------------------------------------------------------------------+
bool IsOrderExpired(datetime order_time)
{
    return (TimeCurrent() - order_time) > (MaxOrderLifeTimeMinutes * 60);
}

//+------------------------------------------------------------------+
//| Manage existing positions                                       |
//+------------------------------------------------------------------+
void ManageExistingPositions()
{
    // This function can be expanded to include position management logic
    // For now, we rely on take profit orders and order expiration
    
    // Count current positions
    int positions = PositionsTotal();
    LogMessage("Current positions: " + IntegerToString(positions));
}

//+------------------------------------------------------------------+
//| Calculate order distance based on market conditions            |
//+------------------------------------------------------------------+
double CalculateOrderDistance()
{
    // Base distance
    double distance = OrderDistancePoints;
    
    // Adjust based on volatility
    double current_volatility = GetMarketVolatility();
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    double volatility_points = current_volatility / point;
    
    // Increase distance in high volatility
    if(volatility_points > 20)
    {
        distance *= 1.5;
    }
    // Decrease distance in low volatility
    else if(volatility_points < 10)
    {
        distance *= 0.8;
    }
    
    return MathMax(10, distance); // Minimum distance of 10 points
}
